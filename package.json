{"name": "sqs-lambda-cdk", "version": "1.0.0", "description": "AWS CDK project with SQS triggering Lambda function and ECS Fargate Express app", "main": "index.js", "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "deploy": "cdk deploy", "destroy": "cdk destroy", "synth": "cdk synth", "start:express": "node express-app/dist/app.js", "build:express": "tsc -p express-app/tsconfig.json", "docker:build": "docker build --platform linux/amd64 -t express-fargate-app ./express-app", "docker:build-and-push": "./scripts/build-and-push-image.sh", "docker:build-and-push-prod": "./scripts/build-and-push-image.sh prod", "deploy:ecs": "cdk deploy EcsFargateAndSpotStack", "deploy:full": "npm run docker:build-and-push && npm run deploy:ecs", "deploy:full-prod": "npm run docker:build-and-push-prod && npm run deploy:ecs"}, "devDependencies": {"@types/jest": "^29.4.0", "@types/node": "18.14.6", "@types/express": "^4.17.17", "@types/cors": "^2.8.13", "jest": "^29.5.0", "ts-jest": "^29.0.5", "aws-cdk": "2.113.0", "ts-node": "^10.9.1", "typescript": "~4.9.5"}, "dependencies": {"aws-cdk-lib": "2.113.0", "constructs": "^10.0.0", "express": "^4.18.2", "winston": "^3.10.0", "cors": "^2.8.5", "helmet": "^7.0.0"}, "keywords": ["aws", "cdk", "sqs", "lambda", "typescript", "ecs", "fargate", "express"], "author": "", "license": "MIT"}