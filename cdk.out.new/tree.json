{"version": "tree-0.1", "tree": {"id": "App", "path": "", "children": {"SqsLambdaStack": {"id": "SqsLambdaStack", "path": "SqsLambdaStack", "children": {"ProcessingDeadLetterQueue": {"id": "ProcessingDeadLetterQueue", "path": "SqsLambdaStack/ProcessingDeadLetterQueue", "children": {"Resource": {"id": "Resource", "path": "SqsLambdaStack/ProcessingDeadLetterQueue/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SQS::Queue", "aws:cdk:cloudformation:props": {"messageRetentionPeriod": 1209600, "queueName": "sqs-lambda-dlq"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_sqs.CfnQueue", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_sqs.Queue", "version": "2.113.0"}}, "ProcessingQueue": {"id": "ProcessingQueue", "path": "SqsLambdaStack/ProcessingQueue", "children": {"Resource": {"id": "Resource", "path": "SqsLambdaStack/ProcessingQueue/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SQS::Queue", "aws:cdk:cloudformation:props": {"messageRetentionPeriod": 604800, "queueName": "sqs-lambda-processing-queue", "redrivePolicy": {"deadLetterTargetArn": {"Fn::GetAtt": ["ProcessingDeadLetterQueueE13B1352", "<PERSON><PERSON>"]}, "maxReceiveCount": 3}, "visibilityTimeout": 300}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_sqs.CfnQueue", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_sqs.Queue", "version": "2.113.0"}}, "LambdaLogGroup": {"id": "LambdaLogGroup", "path": "SqsLambdaStack/LambdaLogGroup", "children": {"Resource": {"id": "Resource", "path": "SqsLambdaStack/LambdaLogGroup/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"logGroupName": "/aws/lambda/sqs-message-processor", "retentionInDays": 7}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.113.0"}}, "MessageProcessor": {"id": "MessageProcessor", "path": "SqsLambdaStack/MessageProcessor", "children": {"ServiceRole": {"id": "ServiceRole", "path": "SqsLambdaStack/MessageProcessor/ServiceRole", "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "SqsLambdaStack/MessageProcessor/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.113.0"}}, "Resource": {"id": "Resource", "path": "SqsLambdaStack/MessageProcessor/ServiceRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.113.0"}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "SqsLambdaStack/MessageProcessor/ServiceRole/DefaultPolicy", "children": {"Resource": {"id": "Resource", "path": "SqsLambdaStack/MessageProcessor/ServiceRole/DefaultPolicy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["sqs:ChangeMessageVisibility", "sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:GetQueueUrl", "sqs:ReceiveMessage"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["ProcessingQueue6DC600C3", "<PERSON><PERSON>"]}}, {"Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["LambdaLogGroup2CA1C11F", "<PERSON><PERSON>"]}}], "Version": "2012-10-17"}, "policyName": "MessageProcessorServiceRoleDefaultPolicyE0D2B27E", "roles": [{"Ref": "MessageProcessorServiceRole886AABCD"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.113.0"}}, "Code": {"id": "Code", "path": "SqsLambdaStack/MessageProcessor/Code", "children": {"Stage": {"id": "Stage", "path": "SqsLambdaStack/MessageProcessor/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.113.0"}}, "AssetBucket": {"id": "AssetBucket", "path": "SqsLambdaStack/MessageProcessor/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.113.0"}}, "Resource": {"id": "Resource", "path": "SqsLambdaStack/MessageProcessor/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": "cdk-hnb659fds-assets-590183851534-ap-southeast-1", "s3Key": "ff48a7c499088c21c9514adbe0182079b852c7acc15d6d0d825d373d4fe70693.zip"}, "description": "Processes messages from SQS queue and logs their content", "environment": {"variables": {"QUEUE_URL": {"Ref": "ProcessingQueue6DC600C3"}, "NODE_ENV": "production"}}, "functionName": "sqs-message-processor", "handler": "index.handler", "memorySize": 256, "role": {"Fn::GetAtt": ["MessageProcessorServiceRole886AABCD", "<PERSON><PERSON>"]}, "runtime": "nodejs18.x", "timeout": 30}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.113.0"}}, "SqsEventSource:SqsLambdaStackProcessingQueueF2052067": {"id": "SqsEventSource:SqsLambdaStackProcessingQueueF2052067", "path": "SqsLambdaStack/MessageProcessor/SqsEventSource:SqsLambdaStackProcessingQueueF2052067", "children": {"Resource": {"id": "Resource", "path": "SqsLambdaStack/MessageProcessor/SqsEventSource:SqsLambdaStackProcessingQueueF2052067/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::EventSourceMapping", "aws:cdk:cloudformation:props": {"batchSize": 10, "eventSourceArn": {"Fn::GetAtt": ["ProcessingQueue6DC600C3", "<PERSON><PERSON>"]}, "functionName": {"Ref": "MessageProcessor9DB0E972"}, "functionResponseTypes": ["ReportBatchItemFailures"], "maximumBatchingWindowInSeconds": 5}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnEventSourceMapping", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.EventSourceMapping", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.113.0"}}, "QueueUrl": {"id": "QueueUrl", "path": "SqsLambdaStack/QueueUrl", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.113.0"}}, "QueueArn": {"id": "QueueArn", "path": "SqsLambdaStack/QueueArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.113.0"}}, "LambdaFunctionArn": {"id": "LambdaFunctionArn", "path": "SqsLambdaStack/LambdaFunctionArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.113.0"}}, "DeadLetterQueueUrl": {"id": "DeadLetterQueueUrl", "path": "SqsLambdaStack/DeadLetterQueueUrl", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.113.0"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "SqsLambdaStack/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "SqsLambdaStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.113.0"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "SqsLambdaStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.113.0"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "SqsLambdaStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.113.0"}}, "EcsFargateAndSpotStack": {"id": "EcsFargateAndSpotStack", "path": "EcsFargateAndSpotStack", "children": {"ExpressFargateVpc": {"id": "ExpressFargateVpc", "path": "EcsFargateAndSpotStack/ExpressFargateVpc", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::VPC", "aws:cdk:cloudformation:props": {"cidrBlock": "10.0.0.0/16", "enableDnsHostnames": true, "enableDnsSupport": true, "instanceTenancy": "default", "tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Name", "value": "EcsFargateAndSpotStack/ExpressFargateVpc"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnVPC", "version": "2.113.0"}}, "PublicSubnet1": {"id": "PublicSubnet1", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1", "children": {"Subnet": {"id": "Subnet", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/Subnet", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "ap-southeast-1a", "cidrBlock": "10.0.0.0/24", "mapPublicIpOnLaunch": true, "tags": [{"key": "aws-cdk:subnet-name", "value": "Public"}, {"key": "aws-cdk:subnet-type", "value": "Public"}, {"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Name", "value": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}], "vpcId": {"Ref": "ExpressFargateVpc1C44938E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.113.0"}}, "Acl": {"id": "Acl", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.113.0"}}, "RouteTable": {"id": "RouteTable", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/RouteTable", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Name", "value": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}], "vpcId": {"Ref": "ExpressFargateVpc1C44938E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.113.0"}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/RouteTableAssociation", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "ExpressFargateVpcPublicSubnet1RouteTable9427E8EE"}, "subnetId": {"Ref": "ExpressFargateVpcPublicSubnet1SubnetFC75DEE2"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.113.0"}}, "DefaultRoute": {"id": "DefaultRoute", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/DefaultRoute", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Route", "aws:cdk:cloudformation:props": {"destinationCidrBlock": "0.0.0.0/0", "gatewayId": {"Ref": "ExpressFargateVpcIGW77BF209F"}, "routeTableId": {"Ref": "ExpressFargateVpcPublicSubnet1RouteTable9427E8EE"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRoute", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PublicSubnet", "version": "2.113.0"}}, "PublicSubnet2": {"id": "PublicSubnet2", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2", "children": {"Subnet": {"id": "Subnet", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/Subnet", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "ap-southeast-1b", "cidrBlock": "********/24", "mapPublicIpOnLaunch": true, "tags": [{"key": "aws-cdk:subnet-name", "value": "Public"}, {"key": "aws-cdk:subnet-type", "value": "Public"}, {"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Name", "value": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}], "vpcId": {"Ref": "ExpressFargateVpc1C44938E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.113.0"}}, "Acl": {"id": "Acl", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.113.0"}}, "RouteTable": {"id": "RouteTable", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/RouteTable", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Name", "value": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}], "vpcId": {"Ref": "ExpressFargateVpc1C44938E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.113.0"}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/RouteTableAssociation", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "ExpressFargateVpcPublicSubnet2RouteTableACD627CE"}, "subnetId": {"Ref": "ExpressFargateVpcPublicSubnet2SubnetFE11C01B"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.113.0"}}, "DefaultRoute": {"id": "DefaultRoute", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/DefaultRoute", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Route", "aws:cdk:cloudformation:props": {"destinationCidrBlock": "0.0.0.0/0", "gatewayId": {"Ref": "ExpressFargateVpcIGW77BF209F"}, "routeTableId": {"Ref": "ExpressFargateVpcPublicSubnet2RouteTableACD627CE"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRoute", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PublicSubnet", "version": "2.113.0"}}, "PublicSubnet3": {"id": "PublicSubnet3", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3", "children": {"Subnet": {"id": "Subnet", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/Subnet", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "ap-southeast-1c", "cidrBlock": "********/24", "mapPublicIpOnLaunch": true, "tags": [{"key": "aws-cdk:subnet-name", "value": "Public"}, {"key": "aws-cdk:subnet-type", "value": "Public"}, {"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Name", "value": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}], "vpcId": {"Ref": "ExpressFargateVpc1C44938E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.113.0"}}, "Acl": {"id": "Acl", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.113.0"}}, "RouteTable": {"id": "RouteTable", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/RouteTable", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Name", "value": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}], "vpcId": {"Ref": "ExpressFargateVpc1C44938E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.113.0"}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/RouteTableAssociation", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "ExpressFargateVpcPublicSubnet3RouteTable5494A407"}, "subnetId": {"Ref": "ExpressFargateVpcPublicSubnet3SubnetCE711AFF"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.113.0"}}, "DefaultRoute": {"id": "DefaultRoute", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/DefaultRoute", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Route", "aws:cdk:cloudformation:props": {"destinationCidrBlock": "0.0.0.0/0", "gatewayId": {"Ref": "ExpressFargateVpcIGW77BF209F"}, "routeTableId": {"Ref": "ExpressFargateVpcPublicSubnet3RouteTable5494A407"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRoute", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PublicSubnet", "version": "2.113.0"}}, "IGW": {"id": "IGW", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/IGW", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::InternetGateway", "aws:cdk:cloudformation:props": {"tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Name", "value": "EcsFargateAndSpotStack/ExpressFargateVpc"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnInternetGateway", "version": "2.113.0"}}, "VPCGW": {"id": "VPCGW", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/VPCGW", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::VPCGatewayAttachment", "aws:cdk:cloudformation:props": {"internetGatewayId": {"Ref": "ExpressFargateVpcIGW77BF209F"}, "vpcId": {"Ref": "ExpressFargateVpc1C44938E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnVPCGatewayAttachment", "version": "2.113.0"}}, "RestrictDefaultSecurityGroupCustomResource": {"id": "RestrictDefaultSecurityGroupCustomResource", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/RestrictDefaultSecurityGroupCustomResource", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "EcsFargateAndSpotStack/ExpressFargateVpc/RestrictDefaultSecurityGroupCustomResource/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.Vpc", "version": "2.113.0"}}, "Custom::VpcRestrictDefaultSGCustomResourceProvider": {"id": "Custom::VpcRestrictDefaultSGCustomResourceProvider", "path": "EcsFargateAndSpotStack/Custom::VpcRestrictDefaultSGCustomResourceProvider", "children": {"Staging": {"id": "Staging", "path": "EcsFargateAndSpotStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Staging", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.113.0"}}, "Role": {"id": "Role", "path": "EcsFargateAndSpotStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Role", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.113.0"}}, "Handler": {"id": "Handler", "path": "EcsFargateAndSpotStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Handler", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResourceProvider", "version": "2.113.0"}}, "ExpressAppRepository": {"id": "ExpressAppRepository", "path": "EcsFargateAndSpotStack/ExpressAppRepository", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecr.RepositoryBase", "version": "2.113.0"}}, "ExpressAppLogGroup": {"id": "ExpressAppLogGroup", "path": "EcsFargateAndSpotStack/ExpressAppLogGroup", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressAppLogGroup/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"logGroupName": "/ecs/express-fargate-app", "retentionInDays": 7, "tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.113.0"}}, "ExpressFargateCluster": {"id": "ExpressFargateCluster", "path": "EcsFargateAndSpotStack/ExpressFargateCluster", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressFargateCluster/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ECS::Cluster", "aws:cdk:cloudformation:props": {"capacityProviders": ["FARGATE", "FARGATE_SPOT"], "clusterName": "express-fargate-cluster", "clusterSettings": [{"name": "containerInsights", "value": "enabled"}], "defaultCapacityProviderStrategy": [{"capacityProvider": "FARGATE", "weight": 1, "base": 1}, {"capacityProvider": "FARGATE_SPOT", "weight": 4}], "tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.CfnCluster", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.Cluster", "version": "2.113.0"}}, "ExpressAppTaskDefinition": {"id": "ExpressAppTaskDefinition", "path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition", "children": {"TaskRole": {"id": "TaskRole", "path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/TaskRole", "children": {"ImportTaskRole": {"id": "ImportTaskRole", "path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/TaskRole/ImportTaskRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.113.0"}}, "Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/TaskRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}, "tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.113.0"}}, "Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ECS::TaskDefinition", "aws:cdk:cloudformation:props": {"containerDefinitions": [{"essential": true, "image": {"Fn::Join": ["", ["590183851534.dkr.ecr.ap-southeast-1.", {"Ref": "AWS::URLSuffix"}, "/express-fargate-app:latest"]]}, "name": "express-app", "portMappings": [{"containerPort": 3000, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": {"Ref": "ExpressAppLogGroupABE13D32"}, "awslogs-stream-prefix": "express-app", "awslogs-region": "ap-southeast-1"}}, "environment": [{"name": "NODE_ENV", "value": "production"}, {"name": "PORT", "value": "3000"}, {"name": "APP_VERSION", "value": "1.0.0"}, {"name": "LOG_LEVEL", "value": "info"}], "healthCheck": {"command": ["CMD-SHELL", "node -e \"const http = require('http'); const options = { hostname: 'localhost', port: 3000, path: '/health', timeout: 3000 }; const req = http.request(options, (res) => { process.exit(res.statusCode === 200 ? 0 : 1); }); req.on('error', () => process.exit(1)); req.on('timeout', () => process.exit(1)); req.end();\""], "interval": 30, "retries": 5, "startPeriod": 120, "timeout": 10}}], "cpu": "512", "executionRoleArn": {"Fn::GetAtt": ["ExpressAppTaskDefinitionExecutionRole920C5CC1", "<PERSON><PERSON>"]}, "family": "express-fargate-app", "memory": "1024", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}], "taskRoleArn": {"Fn::GetAtt": ["ExpressAppTaskDefinitionTaskRole9CFDF9AA", "<PERSON><PERSON>"]}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.CfnTaskDefinition", "version": "2.113.0"}}, "ExpressAppContainer": {"id": "ExpressAppContainer", "path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/ExpressAppContainer", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.ContainerDefinition", "version": "2.113.0"}}, "ExecutionRole": {"id": "ExecutionRole", "path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/ExecutionRole", "children": {"ImportExecutionRole": {"id": "ImportExecutionRole", "path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/ExecutionRole/ImportExecutionRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.113.0"}}, "Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/ExecutionRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}, "tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.113.0"}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/ExecutionRole/DefaultPolicy", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/ExecutionRole/DefaultPolicy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["ecr:BatchCheckLayerAvailability", "ecr:BatchGetImage", "ecr:GetDownloadUrlForLayer"], "Effect": "Allow", "Resource": "arn:aws:ecr:ap-southeast-1:590183851534:repository/express-fargate-app"}, {"Action": "ecr:GetAuthorizationToken", "Effect": "Allow", "Resource": "*"}, {"Action": ["logs:CreateLogStream", "logs:PutLogEvents"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["ExpressAppLogGroupABE13D32", "<PERSON><PERSON>"]}}], "Version": "2012-10-17"}, "policyName": "ExpressAppTaskDefinitionExecutionRoleDefaultPolicy9C75DB36", "roles": [{"Ref": "ExpressAppTaskDefinitionExecutionRole920C5CC1"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.FargateTaskDefinition", "version": "2.113.0"}}, "EcsTaskSecurityGroup": {"id": "EcsTaskSecurityGroup", "path": "EcsFargateAndSpotStack/EcsTaskSecurityGroup", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/EcsTaskSecurityGroup/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroup", "aws:cdk:cloudformation:props": {"groupDescription": "Security group for ECS Fargate tasks in public subnets", "securityGroupEgress": [{"cidrIp": "0.0.0.0/0", "description": "Allow all outbound traffic by default", "ipProtocol": "-1"}], "securityGroupIngress": [{"cidrIp": "0.0.0.0/0", "ipProtocol": "tcp", "fromPort": 3000, "toPort": 3000, "description": "Allow traffic from NLB to ECS tasks"}], "tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}], "vpcId": {"Ref": "ExpressFargateVpc1C44938E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroup", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.SecurityGroup", "version": "2.113.0"}}, "ExpressFargateService": {"id": "ExpressFargateService", "path": "EcsFargateAndSpotStack/ExpressFargateService", "children": {"LB": {"id": "LB", "path": "EcsFargateAndSpotStack/ExpressFargateService/LB", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressFargateService/LB/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::LoadBalancer", "aws:cdk:cloudformation:props": {"loadBalancerAttributes": [{"key": "deletion_protection.enabled", "value": "false"}], "scheme": "internet-facing", "subnets": [{"Ref": "ExpressFargateVpcPublicSubnet1SubnetFC75DEE2"}, {"Ref": "ExpressFargateVpcPublicSubnet2SubnetFE11C01B"}, {"Ref": "ExpressFargateVpcPublicSubnet3SubnetCE711AFF"}], "tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}], "type": "network"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnLoadBalancer", "version": "2.113.0"}}, "PublicListener": {"id": "PublicListener", "path": "EcsFargateAndSpotStack/ExpressFargateService/LB/PublicListener", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressFargateService/LB/PublicListener/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::Listener", "aws:cdk:cloudformation:props": {"defaultActions": [{"type": "forward", "targetGroupArn": {"Ref": "ExpressFargateServiceLBPublicListenerECSGroup706A9F14"}}], "loadBalancerArn": {"Ref": "ExpressFargateServiceLBB3EBA85E"}, "port": 80, "protocol": "TCP"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnListener", "version": "2.113.0"}}, "ECSGroup": {"id": "ECSGroup", "path": "EcsFargateAndSpotStack/ExpressFargateService/LB/PublicListener/ECSGroup", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressFargateService/LB/PublicListener/ECSGroup/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::TargetGroup", "aws:cdk:cloudformation:props": {"healthCheckIntervalSeconds": 30, "healthCheckPath": "/health", "healthCheckPort": "3000", "healthCheckProtocol": "HTTP", "healthCheckTimeoutSeconds": 10, "healthyThresholdCount": 2, "port": 3000, "protocol": "TCP", "tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}], "targetGroupAttributes": [{"key": "deregistration_delay.timeout_seconds", "value": "30"}], "targetType": "ip", "unhealthyThresholdCount": 3, "vpcId": {"Ref": "ExpressFargateVpc1C44938E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnTargetGroup", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.NetworkTargetGroup", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.NetworkListener", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.NetworkLoadBalancer", "version": "2.113.0"}}, "LoadBalancerDNS": {"id": "LoadBalancerDNS", "path": "EcsFargateAndSpotStack/ExpressFargateService/LoadBalancerDNS", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.113.0"}}, "Service": {"id": "Service", "path": "EcsFargateAndSpotStack/ExpressFargateService/Service", "children": {"Service": {"id": "Service", "path": "EcsFargateAndSpotStack/ExpressFargateService/Service/Service", "attributes": {"aws:cdk:cloudformation:type": "AWS::ECS::Service", "aws:cdk:cloudformation:props": {"cluster": {"Ref": "ExpressFargateCluster47B853D6"}, "deploymentConfiguration": {"maximumPercent": 200, "minimumHealthyPercent": 50, "deploymentCircuitBreaker": {"enable": true, "rollback": true}}, "desiredCount": 2, "enableEcsManagedTags": false, "healthCheckGracePeriodSeconds": 600, "launchType": "FARGATE", "loadBalancers": [{"targetGroupArn": {"Ref": "ExpressFargateServiceLBPublicListenerECSGroup706A9F14"}, "containerName": "express-app", "containerPort": 3000}], "networkConfiguration": {"awsvpcConfiguration": {"assignPublicIp": "ENABLED", "subnets": [{"Ref": "ExpressFargateVpcPublicSubnet1SubnetFC75DEE2"}, {"Ref": "ExpressFargateVpcPublicSubnet2SubnetFE11C01B"}, {"Ref": "ExpressFargateVpcPublicSubnet3SubnetCE711AFF"}], "securityGroups": [{"Fn::GetAtt": ["ExpressFargateServiceSecurityGroup7AACBF4E", "GroupId"]}]}}, "platformVersion": "LATEST", "serviceName": "express-fargate-service", "tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}], "taskDefinition": {"Ref": "ExpressAppTaskDefinition16BCAE09"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.CfnService", "version": "2.113.0"}}, "SecurityGroup": {"id": "SecurityGroup", "path": "EcsFargateAndSpotStack/ExpressFargateService/Service/SecurityGroup", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressFargateService/Service/SecurityGroup/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroup", "aws:cdk:cloudformation:props": {"groupDescription": "EcsFargateAndSpotStack/ExpressFargateService/Service/SecurityGroup", "securityGroupEgress": [{"cidrIp": "0.0.0.0/0", "description": "Allow all outbound traffic by default", "ipProtocol": "-1"}], "tags": [{"key": "CostCenter", "value": "Engineering"}, {"key": "Environment", "value": "Development"}, {"key": "Owner", "value": "Dev<PERSON>ps-Team"}, {"key": "Project", "value": "ExpressFargateApp"}, {"key": "Service", "value": "ECS-Fargate"}], "vpcId": {"Ref": "ExpressFargateVpc1C44938E"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroup", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.SecurityGroup", "version": "2.113.0"}}, "ScalingRole": {"id": "ScalingRole", "path": "EcsFargateAndSpotStack/ExpressFargateService/Service/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.113.0"}}, "TaskCount": {"id": "TaskCount", "path": "EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount", "children": {"Target": {"id": "Target", "path": "EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount/Target", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount/Target/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApplicationAutoScaling::ScalableTarget", "aws:cdk:cloudformation:props": {"maxCapacity": 20, "minCapacity": 2, "resourceId": {"Fn::Join": ["", ["service/", {"Ref": "ExpressFargateCluster47B853D6"}, "/", {"Fn::GetAtt": ["ExpressFargateService6EC7E479", "Name"]}]]}, "roleArn": "arn:aws:iam::590183851534:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "scalableDimension": "ecs:service:DesiredCount", "serviceNamespace": "ecs"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_applicationautoscaling.CfnScalableTarget", "version": "2.113.0"}}, "CpuScaling": {"id": "CpuScaling", "path": "EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount/Target/CpuScaling", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount/Target/CpuScaling/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApplicationAutoScaling::ScalingPolicy", "aws:cdk:cloudformation:props": {"policyName": "EcsFargateAndSpotStackExpressFargateServiceTaskCountTargetCpuScalingD6C8D583", "policyType": "TargetTrackingScaling", "scalingTargetId": {"Ref": "ExpressFargateServiceTaskCountTargetA376341A"}, "targetTrackingScalingPolicyConfiguration": {"predefinedMetricSpecification": {"predefinedMetricType": "ECSServiceAverageCPUUtilization"}, "scaleInCooldown": 300, "scaleOutCooldown": 120, "targetValue": 70}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_applicationautoscaling.CfnScalingPolicy", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_applicationautoscaling.TargetTrackingScalingPolicy", "version": "2.113.0"}}, "MemoryScaling": {"id": "MemoryScaling", "path": "EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount/Target/MemoryScaling", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount/Target/MemoryScaling/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApplicationAutoScaling::ScalingPolicy", "aws:cdk:cloudformation:props": {"policyName": "EcsFargateAndSpotStackExpressFargateServiceTaskCountTargetMemoryScaling988C4E44", "policyType": "TargetTrackingScaling", "scalingTargetId": {"Ref": "ExpressFargateServiceTaskCountTargetA376341A"}, "targetTrackingScalingPolicyConfiguration": {"predefinedMetricSpecification": {"predefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "scaleInCooldown": 300, "scaleOutCooldown": 120, "targetValue": 80}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_applicationautoscaling.CfnScalingPolicy", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_applicationautoscaling.TargetTrackingScalingPolicy", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_applicationautoscaling.ScalableTarget", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.ScalableTaskCount", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.FargateService", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs_patterns.NetworkLoadBalancedFargateService", "version": "2.113.0"}}, "ExpressAppVpcLink": {"id": "ExpressAppVpcLink", "path": "EcsFargateAndSpotStack/ExpressAppVpcLink", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressAppVpcLink/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGatewayV2::VpcLink", "aws:cdk:cloudformation:props": {"name": "EcsFargateAndSpotStackExpressAppVpcLinkDF8A280E", "securityGroupIds": [], "subnetIds": [{"Ref": "ExpressFargateVpcPublicSubnet1SubnetFC75DEE2"}, {"Ref": "ExpressFargateVpcPublicSubnet2SubnetFE11C01B"}, {"Ref": "ExpressFargateVpcPublicSubnet3SubnetCE711AFF"}], "tags": {"CostCenter": "Engineering", "Environment": "Development", "Owner": "Dev<PERSON>ps-Team", "Project": "ExpressFargateApp", "Service": "ECS-Fargate"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.CfnVpcLink", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.VpcLink", "version": "2.113.0"}}, "ExpressAppApi": {"id": "ExpressAppApi", "path": "EcsFargateAndSpotStack/ExpressAppApi", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressAppApi/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGatewayV2::Api", "aws:cdk:cloudformation:props": {"corsConfiguration": {"allowHeaders": ["Content-Type", "X-Amz-Date", "Authorization", "X-Api-Key", "X-Amz-Security-Token"], "allowMethods": ["GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH"], "allowOrigins": ["*"]}, "description": "HTTP API Gateway for Express application on ECS Fargate", "name": "Express Fargate API", "protocolType": "HTTP", "tags": {"CostCenter": "Engineering", "Environment": "Development", "Owner": "Dev<PERSON>ps-Team", "Project": "ExpressFargateApp", "Service": "ECS-Fargate"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.CfnApi", "version": "2.113.0"}}, "DefaultStage": {"id": "DefaultStage", "path": "EcsFargateAndSpotStack/ExpressAppApi/DefaultStage", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressAppApi/DefaultStage/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGatewayV2::Stage", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "ExpressAppApi1E1D08A4"}, "autoDeploy": true, "stageName": "$default", "tags": {"CostCenter": "Engineering", "Environment": "Development", "Owner": "Dev<PERSON>ps-Team", "Project": "ExpressFargateApp", "Service": "ECS-Fargate"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.CfnStage", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.HttpStage", "version": "2.113.0"}}, "ANY--{proxy+}": {"id": "ANY--{proxy+}", "path": "EcsFargateAndSpotStack/ExpressAppApi/ANY--{proxy+}", "children": {"NlbIntegration": {"id": "NlbIntegration", "path": "EcsFargateAndSpotStack/ExpressAppApi/ANY--{proxy+}/NlbIntegration", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressAppApi/ANY--{proxy+}/NlbIntegration/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGatewayV2::Integration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "ExpressAppApi1E1D08A4"}, "connectionId": {"Ref": "ExpressAppVpcLink222C994A"}, "connectionType": "VPC_LINK", "integrationMethod": "ANY", "integrationType": "HTTP_PROXY", "integrationUri": {"Ref": "ExpressFargateServiceLBPublicListener499890D0"}, "payloadFormatVersion": "1.0"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.CfnIntegration", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.HttpIntegration", "version": "2.113.0"}}, "Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressAppApi/ANY--{proxy+}/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGatewayV2::Route", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "ExpressAppApi1E1D08A4"}, "authorizationType": "NONE", "routeKey": "ANY /{proxy+}", "target": {"Fn::Join": ["", ["integrations/", {"Ref": "ExpressAppApiANYproxyNlbIntegration4CE66D87"}]]}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.CfnRoute", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.HttpRoute", "version": "2.113.0"}}, "ANY--": {"id": "ANY--", "path": "EcsFargateAndSpotStack/ExpressAppApi/ANY--", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressAppApi/ANY--/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGatewayV2::Route", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "ExpressAppApi1E1D08A4"}, "authorizationType": "NONE", "routeKey": "ANY /", "target": {"Fn::Join": ["", ["integrations/", {"Ref": "ExpressAppApiANYproxyNlbIntegration4CE66D87"}]]}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.CfnRoute", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.HttpRoute", "version": "2.113.0"}}, "prod": {"id": "prod", "path": "EcsFargateAndSpotStack/ExpressAppApi/prod", "children": {"Resource": {"id": "Resource", "path": "EcsFargateAndSpotStack/ExpressAppApi/prod/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGatewayV2::Stage", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "ExpressAppApi1E1D08A4"}, "defaultRouteSettings": {"throttlingBurstLimit": 2000, "throttlingRateLimit": 1000}, "stageName": "prod", "tags": {"CostCenter": "Engineering", "Environment": "Development", "Owner": "Dev<PERSON>ps-Team", "Project": "ExpressFargateApp", "Service": "ECS-Fargate"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.CfnStage", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.HttpStage", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_apigatewayv2.HttpApi", "version": "2.113.0"}}, "ApiGatewayURL": {"id": "ApiGatewayURL", "path": "EcsFargateAndSpotStack/ApiGatewayURL", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.113.0"}}, "NetworkLoadBalancerDNS": {"id": "NetworkLoadBalancerDNS", "path": "EcsFargateAndSpotStack/NetworkLoadBalancerDNS", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.113.0"}}, "ECRRepositoryURI": {"id": "ECRRepositoryURI", "path": "EcsFargateAndSpotStack/ECRRepositoryURI", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.113.0"}}, "ClusterName": {"id": "ClusterName", "path": "EcsFargateAndSpotStack/ClusterName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.113.0"}}, "ServiceName": {"id": "ServiceName", "path": "EcsFargateAndSpotStack/ServiceName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.113.0"}}, "HealthCheckUrl": {"id": "HealthCheckUrl", "path": "EcsFargateAndSpotStack/HealthCheckUrl", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.113.0"}}, "VpcLinkId": {"id": "VpcLinkId", "path": "EcsFargateAndSpotStack/VpcLinkId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.113.0"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "EcsFargateAndSpotStack/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "EcsFargateAndSpotStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.113.0"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "EcsFargateAndSpotStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.113.0"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "EcsFargateAndSpotStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.113.0"}}}, "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.113.0"}}, "Tree": {"id": "Tree", "path": "Tree", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}}, "constructInfo": {"fqn": "aws-cdk-lib.App", "version": "2.113.0"}}}