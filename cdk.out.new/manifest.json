{"version": "35.0.0", "artifacts": {"SqsLambdaStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "SqsLambdaStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "SqsLambdaStack": {"type": "aws:cloudformation:stack", "environment": "aws://590183851534/ap-southeast-1", "properties": {"templateFile": "SqsLambdaStack.template.json", "terminationProtection": false, "tags": {"Environment": "Development", "Owner": "CDK-User", "Project": "SQS-Lambda-Demo"}, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::590183851534:role/cdk-hnb659fds-deploy-role-590183851534-ap-southeast-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::590183851534:role/cdk-hnb659fds-cfn-exec-role-590183851534-ap-southeast-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-590183851534-ap-southeast-1/81f6444d9b3760159ace09ca48c09477f95e4da51e08501f20b847c53c763c90.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["SqsLambdaStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::590183851534:role/cdk-hnb659fds-lookup-role-590183851534-ap-southeast-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["SqsLambdaStack.assets"], "metadata": {"/SqsLambdaStack": [{"type": "aws:cdk:stack-tags", "data": [{"Key": "Environment", "Value": "Development"}, {"Key": "Owner", "Value": "CDK-User"}, {"Key": "Project", "Value": "SQS-Lambda-Demo"}]}], "/SqsLambdaStack/ProcessingDeadLetterQueue/Resource": [{"type": "aws:cdk:logicalId", "data": "ProcessingDeadLetterQueueE13B1352"}], "/SqsLambdaStack/ProcessingQueue/Resource": [{"type": "aws:cdk:logicalId", "data": "ProcessingQueue6DC600C3"}], "/SqsLambdaStack/LambdaLogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "LambdaLogGroup2CA1C11F"}], "/SqsLambdaStack/MessageProcessor/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "MessageProcessorServiceRole886AABCD"}], "/SqsLambdaStack/MessageProcessor/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "MessageProcessorServiceRoleDefaultPolicyE0D2B27E"}], "/SqsLambdaStack/MessageProcessor/Resource": [{"type": "aws:cdk:logicalId", "data": "MessageProcessor9DB0E972"}], "/SqsLambdaStack/MessageProcessor/SqsEventSource:SqsLambdaStackProcessingQueueF2052067/Resource": [{"type": "aws:cdk:logicalId", "data": "MessageProcessorSqsEventSourceSqsLambdaStackProcessingQueueF20520678E255B6C"}], "/SqsLambdaStack/QueueUrl": [{"type": "aws:cdk:logicalId", "data": "QueueUrl"}], "/SqsLambdaStack/QueueArn": [{"type": "aws:cdk:logicalId", "data": "QueueArn"}], "/SqsLambdaStack/LambdaFunctionArn": [{"type": "aws:cdk:logicalId", "data": "LambdaFunctionArn"}], "/SqsLambdaStack/DeadLetterQueueUrl": [{"type": "aws:cdk:logicalId", "data": "DeadLetterQueueUrl"}], "/SqsLambdaStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/SqsLambdaStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/SqsLambdaStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "SqsLambdaStack"}, "EcsFargateAndSpotStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "EcsFargateAndSpotStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "EcsFargateAndSpotStack": {"type": "aws:cloudformation:stack", "environment": "aws://590183851534/ap-southeast-1", "properties": {"templateFile": "EcsFargateAndSpotStack.template.json", "terminationProtection": false, "tags": {"CostCenter": "Engineering", "Environment": "Development", "Owner": "Dev<PERSON>ps-Team", "Project": "ExpressFargateApp", "Service": "ECS-Fargate"}, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::590183851534:role/cdk-hnb659fds-deploy-role-590183851534-ap-southeast-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::590183851534:role/cdk-hnb659fds-cfn-exec-role-590183851534-ap-southeast-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-590183851534-ap-southeast-1/11a0bd12745ef2d943e541de226ef14ec08a9af54ee74e311197e60b358cf823.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["EcsFargateAndSpotStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::590183851534:role/cdk-hnb659fds-lookup-role-590183851534-ap-southeast-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["EcsFargateAndSpotStack.assets"], "metadata": {"/EcsFargateAndSpotStack": [{"type": "aws:cdk:stack-tags", "data": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}]}], "/EcsFargateAndSpotStack/ExpressFargateVpc/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpc1C44938E"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/Subnet": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcPublicSubnet1SubnetFC75DEE2"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/RouteTable": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcPublicSubnet1RouteTable9427E8EE"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcPublicSubnet1RouteTableAssociation5A00D2D7"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcPublicSubnet1DefaultRouteAEE79ACD"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/Subnet": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcPublicSubnet2SubnetFE11C01B"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/RouteTable": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcPublicSubnet2RouteTableACD627CE"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcPublicSubnet2RouteTableAssociation928462C8"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcPublicSubnet2DefaultRouteCDC97F0F"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/Subnet": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcPublicSubnet3SubnetCE711AFF"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/RouteTable": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcPublicSubnet3RouteTable5494A407"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcPublicSubnet3RouteTableAssociationDEAA885A"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcPublicSubnet3DefaultRoute59A0313C"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/IGW": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcIGW77BF209F"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/VPCGW": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcVPCGWAE3CFFE1"}], "/EcsFargateAndSpotStack/ExpressFargateVpc/RestrictDefaultSecurityGroupCustomResource/Default": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateVpcRestrictDefaultSecurityGroupCustomResourceB556BC39"}], "/EcsFargateAndSpotStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Role": [{"type": "aws:cdk:logicalId", "data": "CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0"}], "/EcsFargateAndSpotStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Handler": [{"type": "aws:cdk:logicalId", "data": "CustomVpcRestrictDefaultSGCustomResourceProviderHandlerDC833E5E"}], "/EcsFargateAndSpotStack/ExpressAppLogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressAppLogGroupABE13D32"}], "/EcsFargateAndSpotStack/ExpressFargateCluster/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateCluster47B853D6"}], "/EcsFargateAndSpotStack/ExpressAppTaskDefinition/TaskRole/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressAppTaskDefinitionTaskRole9CFDF9AA"}], "/EcsFargateAndSpotStack/ExpressAppTaskDefinition/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressAppTaskDefinition16BCAE09"}], "/EcsFargateAndSpotStack/ExpressAppTaskDefinition/ExecutionRole/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressAppTaskDefinitionExecutionRole920C5CC1"}], "/EcsFargateAndSpotStack/ExpressAppTaskDefinition/ExecutionRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressAppTaskDefinitionExecutionRoleDefaultPolicy9C75DB36"}], "/EcsFargateAndSpotStack/EcsTaskSecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "EcsTaskSecurityGroup03593AEC"}], "/EcsFargateAndSpotStack/ExpressFargateService/LB/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateServiceLBB3EBA85E"}], "/EcsFargateAndSpotStack/ExpressFargateService/LB/PublicListener/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateServiceLBPublicListener499890D0"}], "/EcsFargateAndSpotStack/ExpressFargateService/LB/PublicListener/ECSGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateServiceLBPublicListenerECSGroup706A9F14"}], "/EcsFargateAndSpotStack/ExpressFargateService/LoadBalancerDNS": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateServiceLoadBalancerDNS5A4610E8"}], "/EcsFargateAndSpotStack/ExpressFargateService/Service/Service": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateService6EC7E479"}], "/EcsFargateAndSpotStack/ExpressFargateService/Service/SecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateServiceSecurityGroup7AACBF4E"}], "/EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount/Target/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateServiceTaskCountTargetA376341A"}], "/EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount/Target/CpuScaling/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateServiceTaskCountTargetCpuScaling755EA309"}], "/EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount/Target/MemoryScaling/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressFargateServiceTaskCountTargetMemoryScaling798A4412"}], "/EcsFargateAndSpotStack/ExpressAppVpcLink/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressAppVpcLink222C994A"}], "/EcsFargateAndSpotStack/ExpressAppApi/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressAppApi1E1D08A4"}], "/EcsFargateAndSpotStack/ExpressAppApi/DefaultStage/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressAppApiDefaultStageA1D516AD"}], "/EcsFargateAndSpotStack/ExpressAppApi/ANY--{proxy+}/NlbIntegration/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressAppApiANYproxyNlbIntegration4CE66D87"}], "/EcsFargateAndSpotStack/ExpressAppApi/ANY--{proxy+}/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressAppApiANYproxy8926B635"}], "/EcsFargateAndSpotStack/ExpressAppApi/ANY--/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressAppApiANY0B88EE22"}], "/EcsFargateAndSpotStack/ExpressAppApi/prod/Resource": [{"type": "aws:cdk:logicalId", "data": "ExpressAppApiprod8D59AF03"}], "/EcsFargateAndSpotStack/ApiGatewayURL": [{"type": "aws:cdk:logicalId", "data": "ApiGatewayURL"}], "/EcsFargateAndSpotStack/NetworkLoadBalancerDNS": [{"type": "aws:cdk:logicalId", "data": "NetworkLoadBalancerDNS"}], "/EcsFargateAndSpotStack/ECRRepositoryURI": [{"type": "aws:cdk:logicalId", "data": "ECRRepositoryURI"}], "/EcsFargateAndSpotStack/ClusterName": [{"type": "aws:cdk:logicalId", "data": "ClusterName"}], "/EcsFargateAndSpotStack/ServiceName": [{"type": "aws:cdk:logicalId", "data": "ServiceName"}], "/EcsFargateAndSpotStack/HealthCheckUrl": [{"type": "aws:cdk:logicalId", "data": "HealthCheckUrl"}], "/EcsFargateAndSpotStack/VpcLinkId": [{"type": "aws:cdk:logicalId", "data": "VpcLinkId"}], "/EcsFargateAndSpotStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/EcsFargateAndSpotStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/EcsFargateAndSpotStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "EcsFargateAndSpotStack"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}}}