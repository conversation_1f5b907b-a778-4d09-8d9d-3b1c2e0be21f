{"Description": "Express application on ECS Fargate with Spot instances", "Resources": {"ExpressFargateVpc1C44938E": {"Type": "AWS::EC2::VPC", "Properties": {"CidrBlock": "10.0.0.0/16", "EnableDnsHostnames": true, "EnableDnsSupport": true, "InstanceTenancy": "default", "Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Name", "Value": "EcsFargateAndSpotStack/ExpressFargateVpc"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}]}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/Resource"}}, "ExpressFargateVpcPublicSubnet1SubnetFC75DEE2": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "ap-southeast-1a", "CidrBlock": "10.0.0.0/24", "MapPublicIpOnLaunch": true, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Public"}, {"Key": "aws-cdk:subnet-type", "Value": "Public"}, {"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Name", "Value": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}], "VpcId": {"Ref": "ExpressFargateVpc1C44938E"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/Subnet"}}, "ExpressFargateVpcPublicSubnet1RouteTable9427E8EE": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Name", "Value": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}], "VpcId": {"Ref": "ExpressFargateVpc1C44938E"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/RouteTable"}}, "ExpressFargateVpcPublicSubnet1RouteTableAssociation5A00D2D7": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "ExpressFargateVpcPublicSubnet1RouteTable9427E8EE"}, "SubnetId": {"Ref": "ExpressFargateVpcPublicSubnet1SubnetFC75DEE2"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/RouteTableAssociation"}}, "ExpressFargateVpcPublicSubnet1DefaultRouteAEE79ACD": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "GatewayId": {"Ref": "ExpressFargateVpcIGW77BF209F"}, "RouteTableId": {"Ref": "ExpressFargateVpcPublicSubnet1RouteTable9427E8EE"}}, "DependsOn": ["ExpressFargateVpcVPCGWAE3CFFE1"], "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet1/DefaultRoute"}}, "ExpressFargateVpcPublicSubnet2SubnetFE11C01B": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "ap-southeast-1b", "CidrBlock": "********/24", "MapPublicIpOnLaunch": true, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Public"}, {"Key": "aws-cdk:subnet-type", "Value": "Public"}, {"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Name", "Value": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}], "VpcId": {"Ref": "ExpressFargateVpc1C44938E"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/Subnet"}}, "ExpressFargateVpcPublicSubnet2RouteTableACD627CE": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Name", "Value": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}], "VpcId": {"Ref": "ExpressFargateVpc1C44938E"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/RouteTable"}}, "ExpressFargateVpcPublicSubnet2RouteTableAssociation928462C8": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "ExpressFargateVpcPublicSubnet2RouteTableACD627CE"}, "SubnetId": {"Ref": "ExpressFargateVpcPublicSubnet2SubnetFE11C01B"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/RouteTableAssociation"}}, "ExpressFargateVpcPublicSubnet2DefaultRouteCDC97F0F": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "GatewayId": {"Ref": "ExpressFargateVpcIGW77BF209F"}, "RouteTableId": {"Ref": "ExpressFargateVpcPublicSubnet2RouteTableACD627CE"}}, "DependsOn": ["ExpressFargateVpcVPCGWAE3CFFE1"], "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet2/DefaultRoute"}}, "ExpressFargateVpcPublicSubnet3SubnetCE711AFF": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "ap-southeast-1c", "CidrBlock": "********/24", "MapPublicIpOnLaunch": true, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Public"}, {"Key": "aws-cdk:subnet-type", "Value": "Public"}, {"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Name", "Value": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}], "VpcId": {"Ref": "ExpressFargateVpc1C44938E"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/Subnet"}}, "ExpressFargateVpcPublicSubnet3RouteTable5494A407": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Name", "Value": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}], "VpcId": {"Ref": "ExpressFargateVpc1C44938E"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/RouteTable"}}, "ExpressFargateVpcPublicSubnet3RouteTableAssociationDEAA885A": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "ExpressFargateVpcPublicSubnet3RouteTable5494A407"}, "SubnetId": {"Ref": "ExpressFargateVpcPublicSubnet3SubnetCE711AFF"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/RouteTableAssociation"}}, "ExpressFargateVpcPublicSubnet3DefaultRoute59A0313C": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "GatewayId": {"Ref": "ExpressFargateVpcIGW77BF209F"}, "RouteTableId": {"Ref": "ExpressFargateVpcPublicSubnet3RouteTable5494A407"}}, "DependsOn": ["ExpressFargateVpcVPCGWAE3CFFE1"], "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/PublicSubnet3/DefaultRoute"}}, "ExpressFargateVpcIGW77BF209F": {"Type": "AWS::EC2::InternetGateway", "Properties": {"Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Name", "Value": "EcsFargateAndSpotStack/ExpressFargateVpc"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}]}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/IGW"}}, "ExpressFargateVpcVPCGWAE3CFFE1": {"Type": "AWS::EC2::VPCGatewayAttachment", "Properties": {"InternetGatewayId": {"Ref": "ExpressFargateVpcIGW77BF209F"}, "VpcId": {"Ref": "ExpressFargateVpc1C44938E"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/VPCGW"}}, "ExpressFargateVpcRestrictDefaultSecurityGroupCustomResourceB556BC39": {"Type": "Custom::VpcRestrictDefaultSG", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomVpcRestrictDefaultSGCustomResourceProviderHandlerDC833E5E", "<PERSON><PERSON>"]}, "DefaultSecurityGroupId": {"Fn::GetAtt": ["ExpressFargateVpc1C44938E", "DefaultSecurityGroup"]}, "Account": "************"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateVpc/RestrictDefaultSecurityGroupCustomResource/Default"}}, "CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}]}, "ManagedPolicyArns": [{"Fn::Sub": "arn:${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"}], "Policies": [{"PolicyName": "Inline", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ec2:AuthorizeSecurityGroupIngress", "ec2:AuthorizeSecurityGroupEgress", "ec2:RevokeSecurityGroupIngress", "ec2:RevokeSecurityGroupEgress"], "Resource": [{"Fn::Join": ["", ["arn:aws:ec2:ap-southeast-1:************:security-group/", {"Fn::GetAtt": ["ExpressFargateVpc1C44938E", "DefaultSecurityGroup"]}]]}]}]}}]}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Role"}}, "CustomVpcRestrictDefaultSGCustomResourceProviderHandlerDC833E5E": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-************-ap-southeast-1", "S3Key": "dd5711540f04e06aa955d7f4862fc04e8cdea464cb590dae91ed2976bb78098e.zip"}, "Timeout": 900, "MemorySize": 128, "Handler": "__entrypoint__.handler", "Role": {"Fn::GetAtt": ["CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Description": "Lambda function for removing all inbound/outbound rules from the VPC default security group"}, "DependsOn": ["CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0"], "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Handler", "aws:asset:path": "asset.dd5711540f04e06aa955d7f4862fc04e8cdea464cb590dae91ed2976bb78098e", "aws:asset:property": "Code"}}, "ExpressAppLogGroupABE13D32": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/ecs/express-fargate-app", "RetentionInDays": 7, "Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressAppLogGroup/Resource"}}, "ExpressFargateCluster47B853D6": {"Type": "AWS::ECS::Cluster", "Properties": {"CapacityProviders": ["FARGATE", "FARGATE_SPOT"], "ClusterName": "express-fargate-cluster", "ClusterSettings": [{"Name": "containerInsights", "Value": "enabled"}], "DefaultCapacityProviderStrategy": [{"Base": 1, "CapacityProvider": "FARGATE", "Weight": 1}, {"CapacityProvider": "FARGATE_SPOT", "Weight": 4}], "Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}]}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateCluster/Resource"}}, "ExpressAppTaskDefinitionTaskRole9CFDF9AA": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}, "Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}]}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/TaskRole/Resource"}}, "ExpressAppTaskDefinition16BCAE09": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "NODE_ENV", "Value": "production"}, {"Name": "PORT", "Value": "3000"}, {"Name": "APP_VERSION", "Value": "1.0.0"}, {"Name": "LOG_LEVEL", "Value": "info"}], "Essential": true, "HealthCheck": {"Command": ["CMD-SHELL", "node -e \"const http = require('http'); const options = { hostname: 'localhost', port: 3000, path: '/health', timeout: 3000 }; const req = http.request(options, (res) => { process.exit(res.statusCode === 200 ? 0 : 1); }); req.on('error', () => process.exit(1)); req.on('timeout', () => process.exit(1)); req.end();\""], "Interval": 30, "Retries": 5, "StartPeriod": 120, "Timeout": 10}, "Image": {"Fn::Join": ["", ["************.dkr.ecr.ap-southeast-1.", {"Ref": "AWS::URLSuffix"}, "/express-fargate-app:latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "ExpressAppLogGroupABE13D32"}, "awslogs-stream-prefix": "express-app", "awslogs-region": "ap-southeast-1"}}, "Name": "express-app", "PortMappings": [{"ContainerPort": 3000, "Protocol": "tcp"}]}], "Cpu": "512", "ExecutionRoleArn": {"Fn::GetAtt": ["ExpressAppTaskDefinitionExecutionRole920C5CC1", "<PERSON><PERSON>"]}, "Family": "express-fargate-app", "Memory": "1024", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}], "TaskRoleArn": {"Fn::GetAtt": ["ExpressAppTaskDefinitionTaskRole9CFDF9AA", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/Resource"}}, "ExpressAppTaskDefinitionExecutionRole920C5CC1": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}, "Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}]}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/ExecutionRole/Resource"}}, "ExpressAppTaskDefinitionExecutionRoleDefaultPolicy9C75DB36": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["ecr:BatchCheckLayerAvailability", "ecr:BatchGetImage", "ecr:GetDownloadUrlForLayer"], "Effect": "Allow", "Resource": "arn:aws:ecr:ap-southeast-1:************:repository/express-fargate-app"}, {"Action": "ecr:GetAuthorizationToken", "Effect": "Allow", "Resource": "*"}, {"Action": ["logs:CreateLogStream", "logs:PutLogEvents"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["ExpressAppLogGroupABE13D32", "<PERSON><PERSON>"]}}], "Version": "2012-10-17"}, "PolicyName": "ExpressAppTaskDefinitionExecutionRoleDefaultPolicy9C75DB36", "Roles": [{"Ref": "ExpressAppTaskDefinitionExecutionRole920C5CC1"}]}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressAppTaskDefinition/ExecutionRole/DefaultPolicy/Resource"}}, "EcsTaskSecurityGroup03593AEC": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Security group for ECS Fargate tasks in public subnets", "SecurityGroupEgress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow all outbound traffic by default", "IpProtocol": "-1"}], "SecurityGroupIngress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow traffic from NLB to ECS tasks", "FromPort": 3000, "IpProtocol": "tcp", "ToPort": 3000}], "Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}], "VpcId": {"Ref": "ExpressFargateVpc1C44938E"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/EcsTaskSecurityGroup/Resource"}}, "ExpressFargateServiceLBB3EBA85E": {"Type": "AWS::ElasticLoadBalancingV2::LoadBalancer", "Properties": {"LoadBalancerAttributes": [{"Key": "deletion_protection.enabled", "Value": "false"}], "Scheme": "internet-facing", "Subnets": [{"Ref": "ExpressFargateVpcPublicSubnet1SubnetFC75DEE2"}, {"Ref": "ExpressFargateVpcPublicSubnet2SubnetFE11C01B"}, {"Ref": "ExpressFargateVpcPublicSubnet3SubnetCE711AFF"}], "Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}], "Type": "network"}, "DependsOn": ["ExpressFargateVpcPublicSubnet1DefaultRouteAEE79ACD", "ExpressFargateVpcPublicSubnet1RouteTableAssociation5A00D2D7", "ExpressFargateVpcPublicSubnet2DefaultRouteCDC97F0F", "ExpressFargateVpcPublicSubnet2RouteTableAssociation928462C8", "ExpressFargateVpcPublicSubnet3DefaultRoute59A0313C", "ExpressFargateVpcPublicSubnet3RouteTableAssociationDEAA885A"], "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateService/LB/Resource"}}, "ExpressFargateServiceLBPublicListener499890D0": {"Type": "AWS::ElasticLoadBalancingV2::Listener", "Properties": {"DefaultActions": [{"TargetGroupArn": {"Ref": "ExpressFargateServiceLBPublicListenerECSGroup706A9F14"}, "Type": "forward"}], "LoadBalancerArn": {"Ref": "ExpressFargateServiceLBB3EBA85E"}, "Port": 80, "Protocol": "TCP"}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateService/LB/PublicListener/Resource"}}, "ExpressFargateServiceLBPublicListenerECSGroup706A9F14": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"HealthCheckIntervalSeconds": 30, "HealthCheckPath": "/health", "HealthCheckPort": "3000", "HealthCheckProtocol": "HTTP", "HealthCheckTimeoutSeconds": 10, "HealthyThresholdCount": 2, "Port": 3000, "Protocol": "TCP", "Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}], "TargetGroupAttributes": [{"Key": "deregistration_delay.timeout_seconds", "Value": "30"}], "TargetType": "ip", "UnhealthyThresholdCount": 3, "VpcId": {"Ref": "ExpressFargateVpc1C44938E"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateService/LB/PublicListener/ECSGroup/Resource"}}, "ExpressFargateService6EC7E479": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Ref": "ExpressFargateCluster47B853D6"}, "DeploymentConfiguration": {"DeploymentCircuitBreaker": {"Enable": true, "Rollback": true}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 2, "EnableECSManagedTags": false, "HealthCheckGracePeriodSeconds": 600, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "express-app", "ContainerPort": 3000, "TargetGroupArn": {"Ref": "ExpressFargateServiceLBPublicListenerECSGroup706A9F14"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "ENABLED", "SecurityGroups": [{"Fn::GetAtt": ["ExpressFargateServiceSecurityGroup7AACBF4E", "GroupId"]}], "Subnets": [{"Ref": "ExpressFargateVpcPublicSubnet1SubnetFC75DEE2"}, {"Ref": "ExpressFargateVpcPublicSubnet2SubnetFE11C01B"}, {"Ref": "ExpressFargateVpcPublicSubnet3SubnetCE711AFF"}]}}, "PlatformVersion": "LATEST", "ServiceName": "express-fargate-service", "Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}], "TaskDefinition": {"Ref": "ExpressAppTaskDefinition16BCAE09"}}, "DependsOn": ["ExpressAppTaskDefinitionTaskRole9CFDF9AA", "ExpressFargateServiceLBPublicListenerECSGroup706A9F14", "ExpressFargateServiceLBPublicListener499890D0"], "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateService/Service/Service"}}, "ExpressFargateServiceSecurityGroup7AACBF4E": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "EcsFargateAndSpotStack/ExpressFargateService/Service/SecurityGroup", "SecurityGroupEgress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow all outbound traffic by default", "IpProtocol": "-1"}], "Tags": [{"Key": "CostCenter", "Value": "Engineering"}, {"Key": "Environment", "Value": "Development"}, {"Key": "Owner", "Value": "Dev<PERSON>ps-Team"}, {"Key": "Project", "Value": "ExpressFargateApp"}, {"Key": "Service", "Value": "ECS-Fargate"}], "VpcId": {"Ref": "ExpressFargateVpc1C44938E"}}, "DependsOn": ["ExpressAppTaskDefinitionTaskRole9CFDF9AA"], "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateService/Service/SecurityGroup/Resource"}}, "ExpressFargateServiceTaskCountTargetA376341A": {"Type": "AWS::ApplicationAutoScaling::ScalableTarget", "Properties": {"MaxCapacity": 20, "MinCapacity": 2, "ResourceId": {"Fn::Join": ["", ["service/", {"Ref": "ExpressFargateCluster47B853D6"}, "/", {"Fn::GetAtt": ["ExpressFargateService6EC7E479", "Name"]}]]}, "RoleARN": "arn:aws:iam::************:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService", "ScalableDimension": "ecs:service:DesiredCount", "ServiceNamespace": "ecs"}, "DependsOn": ["ExpressAppTaskDefinitionTaskRole9CFDF9AA"], "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount/Target/Resource"}}, "ExpressFargateServiceTaskCountTargetCpuScaling755EA309": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "EcsFargateAndSpotStackExpressFargateServiceTaskCountTargetCpuScalingD6C8D583", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "ExpressFargateServiceTaskCountTargetA376341A"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 70}}, "DependsOn": ["ExpressAppTaskDefinitionTaskRole9CFDF9AA"], "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount/Target/CpuScaling/Resource"}}, "ExpressFargateServiceTaskCountTargetMemoryScaling798A4412": {"Type": "AWS::ApplicationAutoScaling::ScalingPolicy", "Properties": {"PolicyName": "EcsFargateAndSpotStackExpressFargateServiceTaskCountTargetMemoryScaling988C4E44", "PolicyType": "TargetTrackingScaling", "ScalingTargetId": {"Ref": "ExpressFargateServiceTaskCountTargetA376341A"}, "TargetTrackingScalingPolicyConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageMemoryUtilization"}, "ScaleInCooldown": 300, "ScaleOutCooldown": 120, "TargetValue": 80}}, "DependsOn": ["ExpressAppTaskDefinitionTaskRole9CFDF9AA"], "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressFargateService/Service/TaskCount/Target/MemoryScaling/Resource"}}, "ExpressAppVpcLink222C994A": {"Type": "AWS::ApiGatewayV2::VpcLink", "Properties": {"Name": "EcsFargateAndSpotStackExpressAppVpcLinkDF8A280E", "SecurityGroupIds": [], "SubnetIds": [{"Ref": "ExpressFargateVpcPublicSubnet1SubnetFC75DEE2"}, {"Ref": "ExpressFargateVpcPublicSubnet2SubnetFE11C01B"}, {"Ref": "ExpressFargateVpcPublicSubnet3SubnetCE711AFF"}], "Tags": {"CostCenter": "Engineering", "Environment": "Development", "Owner": "Dev<PERSON>ps-Team", "Project": "ExpressFargateApp", "Service": "ECS-Fargate"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressAppVpcLink/Resource"}}, "ExpressAppApi1E1D08A4": {"Type": "AWS::ApiGatewayV2::Api", "Properties": {"CorsConfiguration": {"AllowHeaders": ["Content-Type", "X-Amz-Date", "Authorization", "X-Api-Key", "X-Amz-Security-Token"], "AllowMethods": ["GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH"], "AllowOrigins": ["*"]}, "Description": "HTTP API Gateway for Express application on ECS Fargate", "Name": "Express Fargate API", "ProtocolType": "HTTP", "Tags": {"CostCenter": "Engineering", "Environment": "Development", "Owner": "Dev<PERSON>ps-Team", "Project": "ExpressFargateApp", "Service": "ECS-Fargate"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressAppApi/Resource"}}, "ExpressAppApiDefaultStageA1D516AD": {"Type": "AWS::ApiGatewayV2::Stage", "Properties": {"ApiId": {"Ref": "ExpressAppApi1E1D08A4"}, "AutoDeploy": true, "StageName": "$default", "Tags": {"CostCenter": "Engineering", "Environment": "Development", "Owner": "Dev<PERSON>ps-Team", "Project": "ExpressFargateApp", "Service": "ECS-Fargate"}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressAppApi/DefaultStage/Resource"}}, "ExpressAppApiANYproxyNlbIntegration4CE66D87": {"Type": "AWS::ApiGatewayV2::Integration", "Properties": {"ApiId": {"Ref": "ExpressAppApi1E1D08A4"}, "ConnectionId": {"Ref": "ExpressAppVpcLink222C994A"}, "ConnectionType": "VPC_LINK", "IntegrationMethod": "ANY", "IntegrationType": "HTTP_PROXY", "IntegrationUri": {"Ref": "ExpressFargateServiceLBPublicListener499890D0"}, "PayloadFormatVersion": "1.0"}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressAppApi/ANY--{proxy+}/NlbIntegration/Resource"}}, "ExpressAppApiANYproxy8926B635": {"Type": "AWS::ApiGatewayV2::Route", "Properties": {"ApiId": {"Ref": "ExpressAppApi1E1D08A4"}, "AuthorizationType": "NONE", "RouteKey": "ANY /{proxy+}", "Target": {"Fn::Join": ["", ["integrations/", {"Ref": "ExpressAppApiANYproxyNlbIntegration4CE66D87"}]]}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressAppApi/ANY--{proxy+}/Resource"}}, "ExpressAppApiANY0B88EE22": {"Type": "AWS::ApiGatewayV2::Route", "Properties": {"ApiId": {"Ref": "ExpressAppApi1E1D08A4"}, "AuthorizationType": "NONE", "RouteKey": "ANY /", "Target": {"Fn::Join": ["", ["integrations/", {"Ref": "ExpressAppApiANYproxyNlbIntegration4CE66D87"}]]}}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressAppApi/ANY--/Resource"}}, "ExpressAppApiprod8D59AF03": {"Type": "AWS::ApiGatewayV2::Stage", "Properties": {"ApiId": {"Ref": "ExpressAppApi1E1D08A4"}, "DefaultRouteSettings": {"ThrottlingBurstLimit": 2000, "ThrottlingRateLimit": 1000}, "StageName": "prod", "Tags": {"CostCenter": "Engineering", "Environment": "Development", "Owner": "Dev<PERSON>ps-Team", "Project": "ExpressFargateApp", "Service": "ECS-Fargate"}}, "DependsOn": ["ExpressAppApiANYproxyNlbIntegration4CE66D87", "ExpressAppApiANYproxy8926B635", "ExpressAppApiANY0B88EE22", "ExpressAppApiDefaultStageA1D516AD", "ExpressAppApi1E1D08A4"], "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/ExpressAppApi/prod/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/3VS0W7bMAz8lr4rWuPuB9IM6wYEXRAHfS1omdFYK5IhUQ4Cw/8+yHLiNMWeeDweTfrEQi6XT/LxAU5hoepmYaiSfcmgGgGn8N6jKmT/1iqxPti37VpsY2VIlbGyyImb0c5Fxj1UBmd+5lYhOEXA5OxVnMBvy+gt8gswnuA8jZmyFTOov0e0LEpU0ROfX7yL7TjglhgEKi932LpA7Pz5GQIK43SQ/cbpa88FJ3mQ/drEwOhT5QJ/gteQVg7NDzyQpcvC94yzDGTR33BTb4m+I5VNmGCpwCQT0kfWLloeBMFR9juXzRrj1hlSowEZjUu+t8DJoCBfkU/ONxsH9TMYsArru4FoIDAp46CuRgVZ3RWy/9rpsxk3+UVDgdFO9QueanvwGvnq5U06CGhbQ2p8XYjsggJDVst+/vEkHi35zOSw96AasrrMfbMTn4g0hnS+jC5f5YZsM57MBH8xt6uWEpVCSksGnR9jBInKx5dQuj7tr1d5l466YRA7DC769KQxsDvO6cH+p7T1rqMavViFgJwmk9VJ/ydyG3kQ1tUoP8K3rijk8rt8fPgIRAsfLdMR5S7Hf71LxF+cAwAA"}, "Metadata": {"aws:cdk:path": "EcsFargateAndSpotStack/CDKMetadata/Default"}}}, "Outputs": {"ExpressFargateServiceLoadBalancerDNS5A4610E8": {"Value": {"Fn::GetAtt": ["ExpressFargateServiceLBB3EBA85E", "DNSName"]}}, "ApiGatewayURL": {"Description": "HTTP API Gateway URL (primary endpoint)", "Value": {"Fn::Join": ["", ["https://", {"Ref": "ExpressAppApi1E1D08A4"}, ".execute-api.ap-southeast-1.", {"Ref": "AWS::URLSuffix"}, "/"]]}, "Export": {"Name": "ExpressApp-ApiGatewayURL"}}, "NetworkLoadBalancerDNS": {"Description": "Network Load Balancer DNS name", "Value": {"Fn::GetAtt": ["ExpressFargateServiceLBB3EBA85E", "DNSName"]}, "Export": {"Name": "ExpressApp-NetworkLoadBalancerDNS"}}, "ECRRepositoryURI": {"Description": "ECR Repository URI for container images", "Value": {"Fn::Join": ["", ["************.dkr.ecr.ap-southeast-1.", {"Ref": "AWS::URLSuffix"}, "/express-fargate-app"]]}, "Export": {"Name": "ExpressApp-ECRRepositoryURI"}}, "ClusterName": {"Description": "ECS Cluster name", "Value": {"Ref": "ExpressFargateCluster47B853D6"}, "Export": {"Name": "ExpressApp-ClusterName"}}, "ServiceName": {"Description": "ECS Service name", "Value": {"Fn::GetAtt": ["ExpressFargateService6EC7E479", "Name"]}, "Export": {"Name": "ExpressApp-ServiceName"}}, "HealthCheckUrl": {"Description": "Application health check URL via HTTP API Gateway", "Value": {"Fn::Join": ["", ["https://", {"Ref": "ExpressAppApi1E1D08A4"}, ".execute-api.ap-southeast-1.", {"Ref": "AWS::URLSuffix"}, "/health"]]}, "Export": {"Name": "ExpressApp-HealthCheckUrl"}}, "VpcLinkId": {"Description": "VPC Link ID for API Gateway to NLB connectivity", "Value": {"Ref": "ExpressAppVpcLink222C994A"}, "Export": {"Name": "ExpressApp-VpcLinkId"}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}