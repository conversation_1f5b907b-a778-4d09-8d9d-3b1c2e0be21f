{"Description": "Stack demonstrating SQS triggering Lambda function", "Resources": {"ProcessingDeadLetterQueueE13B1352": {"Type": "AWS::SQS::Queue", "Properties": {"MessageRetentionPeriod": 1209600, "QueueName": "sqs-lambda-dlq"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "SqsLambdaStack/ProcessingDeadLetterQueue/Resource"}}, "ProcessingQueue6DC600C3": {"Type": "AWS::SQS::Queue", "Properties": {"MessageRetentionPeriod": 604800, "QueueName": "sqs-lambda-processing-queue", "RedrivePolicy": {"deadLetterTargetArn": {"Fn::GetAtt": ["ProcessingDeadLetterQueueE13B1352", "<PERSON><PERSON>"]}, "maxReceiveCount": 3}, "VisibilityTimeout": 300}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "SqsLambdaStack/ProcessingQueue/Resource"}}, "LambdaLogGroup2CA1C11F": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/lambda/sqs-message-processor", "RetentionInDays": 7}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "SqsLambdaStack/LambdaLogGroup/Resource"}}, "MessageProcessorServiceRole886AABCD": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}, "Metadata": {"aws:cdk:path": "SqsLambdaStack/MessageProcessor/ServiceRole/Resource"}}, "MessageProcessorServiceRoleDefaultPolicyE0D2B27E": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["sqs:ChangeMessageVisibility", "sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:GetQueueUrl", "sqs:ReceiveMessage"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["ProcessingQueue6DC600C3", "<PERSON><PERSON>"]}}, {"Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["LambdaLogGroup2CA1C11F", "<PERSON><PERSON>"]}}], "Version": "2012-10-17"}, "PolicyName": "MessageProcessorServiceRoleDefaultPolicyE0D2B27E", "Roles": [{"Ref": "MessageProcessorServiceRole886AABCD"}]}, "Metadata": {"aws:cdk:path": "SqsLambdaStack/MessageProcessor/ServiceRole/DefaultPolicy/Resource"}}, "MessageProcessor9DB0E972": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-590183851534-ap-southeast-1", "S3Key": "ff48a7c499088c21c9514adbe0182079b852c7acc15d6d0d825d373d4fe70693.zip"}, "Description": "Processes messages from SQS queue and logs their content", "Environment": {"Variables": {"QUEUE_URL": {"Ref": "ProcessingQueue6DC600C3"}, "NODE_ENV": "production"}}, "FunctionName": "sqs-message-processor", "Handler": "index.handler", "MemorySize": 256, "Role": {"Fn::GetAtt": ["MessageProcessorServiceRole886AABCD", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Timeout": 30}, "DependsOn": ["MessageProcessorServiceRoleDefaultPolicyE0D2B27E", "MessageProcessorServiceRole886AABCD"], "Metadata": {"aws:cdk:path": "SqsLambdaStack/MessageProcessor/Resource", "aws:asset:path": "asset.ff48a7c499088c21c9514adbe0182079b852c7acc15d6d0d825d373d4fe70693", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "MessageProcessorSqsEventSourceSqsLambdaStackProcessingQueueF20520678E255B6C": {"Type": "AWS::Lambda::EventSourceMapping", "Properties": {"BatchSize": 10, "EventSourceArn": {"Fn::GetAtt": ["ProcessingQueue6DC600C3", "<PERSON><PERSON>"]}, "FunctionName": {"Ref": "MessageProcessor9DB0E972"}, "FunctionResponseTypes": ["ReportBatchItemFailures"], "MaximumBatchingWindowInSeconds": 5}, "Metadata": {"aws:cdk:path": "SqsLambdaStack/MessageProcessor/SqsEventSource:SqsLambdaStackProcessingQueueF2052067/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/2WN0U7DMAxFv2XvrllbfoAh2AsIaD9g8lJTZU2TUiebUJR/R8kGLzzdYx/Lt8G6bnG7oYtUapgqo48Ye09qArrIIcqXYPwIHBgeP22BBMaNgvHFjfvVhSWLX05gaD4OhPE5WOW1s9n+8dOZre9dWBW/0rJoO2b9f5tA04yxc6bUlnx3RqvvPF4pgbQHEmEv+JADpMVdUBP7HQkn6FjKTyi29zTe+t6CX4Ivj28nCawbGE9yd24arO9xuzmJ1tUarNczY3fNHxYZzZUtAQAA"}, "Metadata": {"aws:cdk:path": "SqsLambdaStack/CDKMetadata/Default"}}}, "Outputs": {"QueueUrl": {"Description": "URL of the SQS queue for sending test messages", "Value": {"Ref": "ProcessingQueue6DC600C3"}, "Export": {"Name": "SqsLambdaQueueUrl"}}, "QueueArn": {"Description": "ARN of the SQS queue", "Value": {"Fn::GetAtt": ["ProcessingQueue6DC600C3", "<PERSON><PERSON>"]}, "Export": {"Name": "SqsLambdaQueueArn"}}, "LambdaFunctionArn": {"Description": "ARN of the Lambda function", "Value": {"Fn::GetAtt": ["MessageProcessor9DB0E972", "<PERSON><PERSON>"]}, "Export": {"Name": "SqsLambdaFunctionArn"}}, "DeadLetterQueueUrl": {"Description": "URL of the Dead Letter Queue for failed messages", "Value": {"Ref": "ProcessingDeadLetterQueueE13B1352"}, "Export": {"Name": "SqsLambdaDeadLetterQueueUrl"}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}