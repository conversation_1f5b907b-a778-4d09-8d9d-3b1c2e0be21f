# ECS Scheduler for Cost Optimization

## 🎯 **Overview**

This guide explains the automated ECS scheduler that optimizes costs by adjusting your Fargate service configuration based on Vietnam business hours.

## 💰 **Cost Optimization Strategy**

### **Business Hours (8 AM - 8 PM Vietnam Time)**
- **Configuration**: 2 tasks total
  - 1 Fargate task (reliable, on-demand)
  - 1 Fargate Spot task (cost-optimized)
- **Purpose**: Full redundancy and performance during peak hours
- **Auto-scaling**: Min 2, Max 20 tasks

### **Off-Hours (8 PM - 8 AM Vietnam Time)**
- **Configuration**: 1 task total
  - 1 Fargate Spot task only
- **Purpose**: Minimal cost while maintaining service availability
- **Auto-scaling**: Min 1, Max 20 tasks

### **Estimated Savings**
- **Daily task-hours reduction**: 25% (from 48 to 36 task-hours)
- **Monthly cost savings**: ~$15-25 USD
- **Annual savings**: ~$180-300 USD

## 🕐 **Schedule Details**

### **Vietnam Timezone (UTC+7)**
| Time Period | Vietnam Time | UTC Time | Configuration |
|-------------|--------------|----------|---------------|
| Business Hours | 8 AM - 8 PM | 1 AM - 1 PM | 2 tasks (1 Fargate + 1 Spot) |
| Off Hours | 8 PM - 8 AM | 1 PM - 1 AM | 1 task (Spot only) |

### **EventBridge Cron Schedules**
- **Business Hours Trigger**: `0 1 * * *` (1 AM UTC daily)
- **Off Hours Trigger**: `0 13 * * *` (1 PM UTC daily)

## 🏗️ **Architecture Components**

### **1. Lambda Functions**
- **BusinessHoursScheduler**: Scales up to 2 tasks with mixed capacity
- **OffHoursScheduler**: Scales down to 1 Spot task

### **2. EventBridge Rules**
- **BusinessHoursRule**: Triggers at 8 AM Vietnam time
- **OffHoursRule**: Triggers at 8 PM Vietnam time

### **3. IAM Permissions**
- ECS service update permissions
- Application Auto Scaling permissions
- CloudWatch Logs permissions

## 🚀 **Deployment**

### **Deploy the Updated Stack**
```bash
# Deploy with the new scheduler
cdk deploy EcsFargateAndSpotStack

# Verify deployment
aws events list-rules --name-prefix "EcsFargateAndSpotStack"
```

### **Verify Scheduler Functions**
```bash
# List the scheduler Lambda functions
aws lambda list-functions --query 'Functions[?contains(FunctionName, `Scheduler`)].FunctionName'

# Check EventBridge rules
aws events list-rules --query 'Rules[?contains(Name, `Hours`)].{Name:Name,Schedule:ScheduleExpression,State:State}'
```

## 📊 **Monitoring**

### **CloudWatch Logs**
Monitor scheduler execution:
```bash
# Business hours scheduler logs
aws logs tail /aws/lambda/EcsFargateAndSpotStack-BusinessHoursScheduler --follow

# Off-hours scheduler logs  
aws logs tail /aws/lambda/EcsFargateAndSpotStack-OffHoursScheduler --follow
```

### **ECS Service Status**
Check current service configuration:
```bash
# Check current desired count and capacity providers
aws ecs describe-services \
  --cluster express-fargate-cluster \
  --services express-fargate-service \
  --query 'services[0].{DesiredCount:desiredCount,RunningCount:runningCount,CapacityProviders:capacityProviderStrategy}'
```

### **Auto Scaling Status**
```bash
# Check auto-scaling configuration
aws application-autoscaling describe-scalable-targets \
  --service-namespace ecs \
  --resource-ids service/express-fargate-cluster/express-fargate-service
```

## 🛠️ **Manual Testing**

### **Test Business Hours Configuration**
```bash
# Manually trigger business hours scheduler
aws lambda invoke \
  --function-name EcsFargateAndSpotStack-BusinessHoursScheduler \
  --payload '{}' \
  response.json && cat response.json
```

### **Test Off-Hours Configuration**
```bash
# Manually trigger off-hours scheduler
aws lambda invoke \
  --function-name EcsFargateAndSpotStack-OffHoursScheduler \
  --payload '{}' \
  response.json && cat response.json
```

### **Verify Configuration Changes**
```bash
# Wait 2-3 minutes after triggering, then check service
aws ecs describe-services \
  --cluster express-fargate-cluster \
  --services express-fargate-service \
  --query 'services[0].{DesiredCount:desiredCount,CapacityProviders:capacityProviderStrategy[*].{Provider:capacityProvider,Weight:weight,Base:base}}'
```

## ⚙️ **Configuration Options**

### **Modify Schedule Times**
To change the business hours, update the cron expressions in the CDK code:

```typescript
// Business hours rule (currently 8 AM Vietnam = 1 AM UTC)
schedule: events.Schedule.cron({
  minute: "0",
  hour: "1",    // Change this for different start time
  day: "*",
  month: "*", 
  year: "*",
})

// Off hours rule (currently 8 PM Vietnam = 1 PM UTC)  
schedule: events.Schedule.cron({
  minute: "0",
  hour: "13",   // Change this for different end time
  day: "*",
  month: "*",
  year: "*", 
})
```

### **Modify Task Counts**
Update environment variables in Lambda functions:
- `DESIRED_COUNT`: Number of tasks for each period
- Capacity provider strategy in Lambda code

### **Weekend Scheduling**
To disable scheduling on weekends, modify cron expressions:
```typescript
// Only run Monday-Friday (1-5)
schedule: events.Schedule.cron({
  minute: "0",
  hour: "1",
  day: "*",
  month: "*",
  year: "*",
  weekDay: "MON-FRI"  // Add this line
})
```

## 🔧 **Troubleshooting**

### **Common Issues**

1. **Scheduler not triggering**
   - Check EventBridge rule state: `aws events describe-rule --name RuleName`
   - Verify Lambda permissions
   - Check CloudWatch Logs for errors

2. **ECS service not updating**
   - Verify IAM permissions for Lambda role
   - Check if service is in DRAINING state
   - Ensure cluster and service names are correct

3. **Auto-scaling conflicts**
   - Manual scaling may override scheduler
   - Check auto-scaling policies and alarms
   - Verify min/max capacity settings

### **Debug Commands**
```bash
# Check EventBridge rule status
aws events describe-rule --name EcsFargateAndSpotStack-BusinessHoursRule

# Check Lambda function configuration
aws lambda get-function --function-name EcsFargateAndSpotStack-BusinessHoursScheduler

# Check ECS service events
aws ecs describe-services \
  --cluster express-fargate-cluster \
  --services express-fargate-service \
  --query 'services[0].events[0:5]'
```

## 📈 **Cost Monitoring**

### **Track Savings**
1. **AWS Cost Explorer**: Filter by ECS service and compare before/after
2. **CloudWatch Metrics**: Monitor task count over time
3. **Billing Alerts**: Set up alerts for unexpected cost increases

### **Expected Cost Pattern**
- **Business Hours**: Higher costs (2 tasks running)
- **Off Hours**: Lower costs (1 task running)
- **Monthly Pattern**: ~25% reduction in compute costs

## 🎯 **Next Steps**

1. **Deploy and Monitor**: Deploy the scheduler and monitor for 1 week
2. **Fine-tune**: Adjust schedules based on actual usage patterns
3. **Expand**: Consider adding weekend-specific schedules
4. **Optimize**: Monitor Spot interruptions and adjust strategy if needed

This scheduler provides significant cost savings while maintaining service reliability during business hours!
