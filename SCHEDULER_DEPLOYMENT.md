# ECS Scheduler Deployment Guide

## 🚀 **Quick Start**

This guide will help you deploy the ECS scheduler for automatic cost optimization based on Vietnam business hours.

## 📋 **Prerequisites**

- AWS CDK installed and configured
- AWS CLI configured with appropriate permissions
- Existing ECS Fargate stack deployed

## 🔧 **Deployment Steps**

### **1. Deploy the Updated Stack**

```bash
# Deploy the stack with the new scheduler
cdk deploy EcsFargateAndSpotStack

# Confirm deployment when prompted
# This will create:
# - 2 Lambda functions (business hours & off-hours schedulers)
# - 2 EventBridge rules for automatic triggering
# - IAM roles and policies
# - CloudWatch log groups
```

### **2. Verify Deployment**

```bash
# Check if scheduler functions were created
aws lambda list-functions --query 'Functions[?contains(FunctionName, `Scheduler`)].FunctionName'

# Check EventBridge rules
aws events list-rules --query 'Rules[?contains(Name, `Hours`)].{Name:Name,Schedule:ScheduleExpression,State:State}'

# Verify IAM role was created
aws iam get-role --role-name EcsFargateAndSpotStack-EcsSchedulerRole
```

### **3. Test the Scheduler**

Use the provided management script:

```bash
# Make script executable (if not already)
chmod +x scripts/ecs-scheduler-manager.sh

# Check current service status
./scripts/ecs-scheduler-manager.sh status

# Test business hours configuration (scales to 2 tasks)
./scripts/ecs-scheduler-manager.sh test-business

# Test off-hours configuration (scales to 1 task)
./scripts/ecs-scheduler-manager.sh test-off
```

## 📊 **Monitoring**

### **Real-time Monitoring**

```bash
# Monitor business hours scheduler logs
./scripts/ecs-scheduler-manager.sh monitor-business

# Monitor off-hours scheduler logs
./scripts/ecs-scheduler-manager.sh monitor-off

# Check EventBridge rules status
./scripts/ecs-scheduler-manager.sh check-rules
```

### **Service Status Monitoring**

```bash
# Check current ECS service configuration
aws ecs describe-services \
  --cluster express-fargate-cluster \
  --services express-fargate-service \
  --query 'services[0].{DesiredCount:desiredCount,RunningCount:runningCount,CapacityProviders:capacityProviderStrategy}'

# Monitor auto-scaling configuration
aws application-autoscaling describe-scalable-targets \
  --service-namespace ecs \
  --resource-ids service/express-fargate-cluster/express-fargate-service
```

## 🕐 **Schedule Verification**

The scheduler operates on Vietnam timezone (UTC+7):

| Event | Vietnam Time | UTC Time | Action |
|-------|--------------|----------|--------|
| Business Hours Start | 8:00 AM | 1:00 AM | Scale to 2 tasks (1 Fargate + 1 Spot) |
| Business Hours End | 8:00 PM | 1:00 PM | Scale to 1 task (Spot only) |

### **Verify Schedule is Working**

1. **Wait for automatic execution** (next scheduled time)
2. **Check CloudWatch Logs** for scheduler execution
3. **Monitor ECS service** for configuration changes

## 🛠️ **Troubleshooting**

### **Common Issues**

1. **Scheduler not triggering**
   ```bash
   # Check EventBridge rule state
   aws events describe-rule --name EcsFargateAndSpotStack-BusinessHoursRule
   
   # Verify rule is ENABLED
   # Check Lambda function permissions
   ```

2. **ECS service not updating**
   ```bash
   # Check Lambda function logs
   aws logs tail /aws/lambda/EcsFargateAndSpotStack-BusinessHoursScheduler
   
   # Verify IAM permissions
   # Check if service is in stable state
   ```

3. **Permission errors**
   ```bash
   # Verify scheduler role has correct permissions
   aws iam list-attached-role-policies --role-name EcsFargateAndSpotStack-EcsSchedulerRole
   
   # Check inline policies
   aws iam list-role-policies --role-name EcsFargateAndSpotStack-EcsSchedulerRole
   ```

### **Manual Override**

If you need to manually set the service configuration:

```bash
# Set to business hours configuration (2 tasks)
aws ecs update-service \
  --cluster express-fargate-cluster \
  --service express-fargate-service \
  --desired-count 2 \
  --capacity-provider-strategy capacityProvider=FARGATE,weight=1,base=1 capacityProvider=FARGATE_SPOT,weight=1

# Set to off-hours configuration (1 task)
aws ecs update-service \
  --cluster express-fargate-cluster \
  --service express-fargate-service \
  --desired-count 1 \
  --capacity-provider-strategy capacityProvider=FARGATE_SPOT,weight=1,base=1
```

## 📈 **Expected Results**

### **Cost Savings Timeline**

- **Immediate**: Scheduler deployed and functional
- **Day 1**: First automatic scaling events
- **Week 1**: Visible cost reduction in AWS billing
- **Month 1**: Full cost savings realized (~25% reduction)

### **Monitoring Metrics**

Track these metrics to verify savings:

1. **ECS Task Count**: Should vary between 1-2 based on time
2. **Fargate vs Spot Usage**: More Spot usage during off-hours
3. **AWS Costs**: Gradual reduction in ECS compute costs

## 🎯 **Next Steps**

1. **Monitor for 1 week** to ensure stable operation
2. **Review cost reports** in AWS Cost Explorer
3. **Fine-tune schedules** if needed based on usage patterns
4. **Consider weekend schedules** for additional savings

## 📞 **Support**

If you encounter issues:

1. Check the troubleshooting section above
2. Review CloudWatch Logs for error messages
3. Verify AWS permissions and service states
4. Use the management script for testing and monitoring

The scheduler is now deployed and will automatically optimize your ECS costs based on Vietnam business hours!
