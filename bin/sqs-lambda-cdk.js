#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("source-map-support/register");
const cdk = require("aws-cdk-lib");
const sqs_lambda_stack_1 = require("../lib/sqs-lambda-stack");
const ecs_fargate_and_spot_stack_1 = require("../lib/ecs-fargate-and-spot-stack");
/**
 * Main CDK application entry point
 * Creates and deploys both SQS-Lambda and ECS Fargate stacks
 */
const app = new cdk.App();
/**
 * Environment configuration for AWS deployment
 * Set your AWS account ID and preferred region
 */
const envConfig = {
    account: "************",
    region: "ap-southeast-1",
};
/**
 * Instantiate the SQS-Lambda stack with environment configuration
 * The stack will be deployed to the default AWS account/region
 */
new sqs_lambda_stack_1.SqsLambdaStack(app, "SqsLambdaStack", {
    env: envConfig,
    description: "Stack demonstrating SQS triggering Lambda function",
    tags: {
        Project: "SQS-Lambda-Demo",
        Environment: "Development",
        Owner: "CDK-User",
    },
});
/**
 * Instantiate the ECS Fargate and Spot stack
 * Deploys Express application with mixed capacity strategies
 */
new ecs_fargate_and_spot_stack_1.EcsFargateAndSpotStack(app, "EcsFargateAndSpotStack", {
    env: envConfig,
    description: "Express application on ECS Fargate with Spot instances",
    tags: {
        Project: "ExpressFargateApp",
        Environment: "Development",
        Owner: "DevOps-Team",
        Service: "ECS-Fargate",
        CostCenter: "Engineering",
    },
});
//# sourceMappingURL=data:application/json;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************