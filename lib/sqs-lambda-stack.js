"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SqsLambdaStack = void 0;
const cdk = require("aws-cdk-lib");
const lambda = require("aws-cdk-lib/aws-lambda");
const sqs = require("aws-cdk-lib/aws-sqs");
const lambdaEventSources = require("aws-cdk-lib/aws-lambda-event-sources");
const iam = require("aws-cdk-lib/aws-iam");
const logs = require("aws-cdk-lib/aws-logs");
/**
 * CDK Stack that creates an SQS queue and Lambda function integration
 * The Lambda function will be triggered whenever messages are sent to the SQS queue
 */
class SqsLambdaStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        // Create SQS Queue with Dead Letter Queue for failed message handling
        const deadLetterQueue = new sqs.Queue(this, "ProcessingDeadLetterQueue", {
            queueName: "sqs-lambda-dlq",
            retentionPeriod: cdk.Duration.days(14), // Keep failed messages for 14 days
        });
        // Main SQS Queue that will trigger the Lambda function
        this.queue = new sqs.Queue(this, "ProcessingQueue", {
            queueName: "sqs-lambda-processing-queue",
            visibilityTimeout: cdk.Duration.seconds(300),
            retentionPeriod: cdk.Duration.days(7),
            deadLetterQueue: {
                queue: deadLetterQueue,
                maxReceiveCount: 3, // Retry failed messages 3 times before moving to DLQ
            },
        });
        // Create CloudWatch Log Group for Lambda function logs
        const logGroup = new logs.LogGroup(this, "LambdaLogGroup", {
            logGroupName: "/aws/lambda/sqs-message-processor",
            retention: logs.RetentionDays.ONE_WEEK,
            removalPolicy: cdk.RemovalPolicy.DESTROY, // For demo purposes
        });
        // Create Lambda function that processes SQS messages
        this.lambdaFunction = new lambda.Function(this, "MessageProcessor", {
            functionName: "sqs-message-processor",
            runtime: lambda.Runtime.NODEJS_18_X,
            handler: "index.handler",
            code: lambda.Code.fromAsset("lambda"),
            timeout: cdk.Duration.seconds(30),
            memorySize: 256,
            // reservedConcurrentExecutions: 10, // Commented out due to account limits - test natural scaling instead
            environment: {
                QUEUE_URL: this.queue.queueUrl,
                NODE_ENV: "production",
            },
            description: "Processes messages from SQS queue and logs their content",
        });
        // Grant Lambda function permissions to consume messages from the SQS queue
        this.queue.grantConsumeMessages(this.lambdaFunction);
        // Add SQS as an event source for the Lambda function
        // This creates the necessary event source mapping
        this.lambdaFunction.addEventSource(new lambdaEventSources.SqsEventSource(this.queue, {
            batchSize: 10,
            maxBatchingWindow: cdk.Duration.seconds(5),
            reportBatchItemFailures: true, // Enable partial batch failure reporting
        }));
        // Grant Lambda additional permissions for CloudWatch Logs (explicit for clarity)
        this.lambdaFunction.addToRolePolicy(new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents",
            ],
            resources: [logGroup.logGroupArn],
        }));
        // Output important resource ARNs and URLs for reference
        new cdk.CfnOutput(this, "QueueUrl", {
            value: this.queue.queueUrl,
            description: "URL of the SQS queue for sending test messages",
            exportName: "SqsLambdaQueueUrl",
        });
        new cdk.CfnOutput(this, "QueueArn", {
            value: this.queue.queueArn,
            description: "ARN of the SQS queue",
            exportName: "SqsLambdaQueueArn",
        });
        new cdk.CfnOutput(this, "LambdaFunctionArn", {
            value: this.lambdaFunction.functionArn,
            description: "ARN of the Lambda function",
            exportName: "SqsLambdaFunctionArn",
        });
        new cdk.CfnOutput(this, "DeadLetterQueueUrl", {
            value: deadLetterQueue.queueUrl,
            description: "URL of the Dead Letter Queue for failed messages",
            exportName: "SqsLambdaDeadLetterQueueUrl",
        });
    }
}
exports.SqsLambdaStack = SqsLambdaStack;
//# sourceMappingURL=data:application/json;base64,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