"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcsFargateAndSpotStack = void 0;
const cdk = require("aws-cdk-lib");
const ec2 = require("aws-cdk-lib/aws-ec2");
const ecs = require("aws-cdk-lib/aws-ecs");
const ecsPatterns = require("aws-cdk-lib/aws-ecs-patterns");
const logs = require("aws-cdk-lib/aws-logs");
const iam = require("aws-cdk-lib/aws-iam");
const ecr = require("aws-cdk-lib/aws-ecr");
const apigatewayv2 = require("aws-cdk-lib/aws-apigatewayv2");
const apigatewayv2Integrations = require("aws-cdk-lib/aws-apigatewayv2-integrations");
const elasticloadbalancingv2 = require("aws-cdk-lib/aws-elasticloadbalancingv2");
const lambda = require("aws-cdk-lib/aws-lambda");
const events = require("aws-cdk-lib/aws-events");
const targets = require("aws-cdk-lib/aws-events-targets");
/**
 * ECS Fargate and Spot Stack
 *
 * Creates a comprehensive container deployment using AWS ECS Fargate
 * Combines both Fargate and Spot capacity strategies for cost optimization
 * Includes networking, load balancing, auto-scaling, and monitoring
 *
 * Features:
 * - Cost-optimized VPC with public subnets only (no NAT Gateway costs)
 * - ECS cluster with mixed capacity providers (Fargate + Spot)
 * - ECS tasks run in public subnets with public IPs
 * - HTTP API Gateway as front door for microservices (70% cheaper, 2x faster)
 * - Network Load Balancer for high performance and lower latency
 * - VPC Link for secure API Gateway to NLB connectivity
 * - Auto-scaling based on CPU and memory utilization
 * - CloudWatch logging and monitoring
 * - ECR repository for container images
 * - Security groups optimized for public subnet deployment
 */
class EcsFargateAndSpotStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        /**
         * VPC Configuration - Cost Optimized
         * Creates a VPC with only public subnets across multiple AZs
         * No NAT Gateways needed - ECS tasks run in public subnets with public IPs
         * Significant cost savings: ~$90/month savings (2 NAT Gateways eliminated)
         */
        const vpc = new ec2.Vpc(this, "ExpressFargateVpc", {
            maxAzs: 3,
            ipAddresses: ec2.IpAddresses.cidr("10.0.0.0/16"),
            natGateways: 0,
            subnetConfiguration: [
                {
                    cidrMask: 24,
                    name: "Public",
                    subnetType: ec2.SubnetType.PUBLIC,
                },
            ],
            enableDnsHostnames: true,
            enableDnsSupport: true,
        });
        // Tag VPC resources for better organization and cost tracking
        cdk.Tags.of(vpc).add("Project", "ExpressFargateApp");
        cdk.Tags.of(vpc).add("Environment", props?.tags?.Environment || "Development");
        /**
         * ECR Repository
         * Use existing repository or create a new one
         * Configured with lifecycle policies for cost optimization
         */
        this.repository = ecr.Repository.fromRepositoryName(this, "ExpressAppRepository", "express-fargate-app");
        /**
         * CloudWatch Log Group
         * Centralized logging for all ECS tasks with retention policy
         */
        const logGroup = new logs.LogGroup(this, "ExpressAppLogGroup", {
            logGroupName: "/ecs/express-fargate-app",
            retention: logs.RetentionDays.ONE_WEEK,
            removalPolicy: cdk.RemovalPolicy.DESTROY, // For development
        });
        /**
         * ECS Cluster Configuration
         * Creates an ECS cluster with mixed capacity providers
         * Supports both Fargate and Fargate Spot for cost optimization
         */
        this.cluster = new ecs.Cluster(this, "ExpressFargateCluster", {
            clusterName: "express-fargate-cluster",
            vpc: vpc,
            containerInsights: true, // Enable CloudWatch Container Insights
        });
        /**
         * Capacity Providers Configuration
         * Defines how tasks are distributed between Fargate and Spot
         * Fargate: Reliable, on-demand capacity
         * Fargate Spot: Cost-optimized, interruptible capacity
         */
        // Enable Fargate and Fargate Spot capacity providers
        const cfnCluster = this.cluster.node.defaultChild;
        cfnCluster.capacityProviders = ["FARGATE", "FARGATE_SPOT"];
        cfnCluster.defaultCapacityProviderStrategy = [
            {
                capacityProvider: "FARGATE",
                weight: 1,
                base: 1, // At least 1 task on Fargate for reliability
            },
            {
                capacityProvider: "FARGATE_SPOT",
                weight: 4, // 80% of tasks on Fargate Spot for cost savings
            },
        ];
        /**
         * Task Definition
         * Defines the container configuration and resource requirements
         * Includes proper logging, environment variables, and health checks
         */
        const taskDefinition = new ecs.FargateTaskDefinition(this, "ExpressAppTaskDefinition", {
            memoryLimitMiB: 1024,
            cpu: 512,
            family: "express-fargate-app",
        });
        // Task execution role for ECR and CloudWatch access
        taskDefinition.executionRole?.addManagedPolicy(iam.ManagedPolicy.fromAwsManagedPolicyName("service-role/AmazonECSTaskExecutionRolePolicy"));
        // Additional permissions for CloudWatch and ECR
        if (taskDefinition.executionRole &&
            "addToPolicy" in taskDefinition.executionRole) {
            taskDefinition.executionRole.addToPolicy(new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    "ecr:GetAuthorizationToken",
                    "ecr:BatchCheckLayerAvailability",
                    "ecr:GetDownloadUrlForLayer",
                    "ecr:BatchGetImage",
                    "logs:CreateLogStream",
                    "logs:PutLogEvents",
                ],
                resources: ["*"],
            }));
        }
        /**
         * Container Definition
         * Configures the Express application container with proper settings
         */
        const container = taskDefinition.addContainer("ExpressAppContainer", {
            image: ecs.ContainerImage.fromEcrRepository(this.repository, "latest"),
            containerName: "express-app",
            environment: {
                NODE_ENV: "production",
                PORT: "3000",
                APP_VERSION: "1.0.0",
                LOG_LEVEL: "info",
            },
            logging: ecs.LogDrivers.awsLogs({
                streamPrefix: "express-app",
                logGroup: logGroup,
            }),
            healthCheck: {
                command: [
                    "CMD-SHELL",
                    "node -e \"const http = require('http'); const options = { hostname: 'localhost', port: 3000, path: '/health', timeout: 3000 }; const req = http.request(options, (res) => { process.exit(res.statusCode === 200 ? 0 : 1); }); req.on('error', () => process.exit(1)); req.on('timeout', () => process.exit(1)); req.end();\"",
                ],
                interval: cdk.Duration.seconds(30),
                timeout: cdk.Duration.seconds(10),
                retries: 5,
                startPeriod: cdk.Duration.seconds(120), // Increased startup period for app initialization
            },
        });
        // Expose container port
        container.addPortMappings({
            containerPort: 3000,
            protocol: ecs.Protocol.TCP,
        });
        /**
         * Security Group for ECS Tasks - Public Subnet Configuration
         * Allows inbound traffic from the load balancer and internet access
         * Tasks run in public subnets with public IPs for cost optimization
         */
        const ecsSecurityGroup = new ec2.SecurityGroup(this, "EcsTaskSecurityGroup", {
            vpc: vpc,
            description: "Security group for ECS Fargate tasks in public subnets",
            allowAllOutbound: true, // Allow outbound for API calls and dependencies
        });
        /**
         * Network Load Balancer Security Group
         * NLB operates at Layer 4 and doesn't need security groups for itself
         * Security is handled at the target (ECS task) level
         */
        // Allow NLB to communicate with ECS tasks
        ecsSecurityGroup.addIngressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(3000), "Allow traffic from NLB to ECS tasks");
        // Optional: Allow direct access to ECS tasks for debugging (remove in production)
        // ecsSecurityGroup.addIngressRule(
        //   ec2.Peer.anyIpv4(),
        //   ec2.Port.tcp(8080),
        //   "Allow direct access to ECS tasks (debugging only)"
        // );
        /**
         * Fargate Service with Network Load Balancer - High Performance
         * Tasks run in public subnets with public IPs (no NAT Gateway needed)
         * NLB provides higher performance and lower latency than ALB
         */
        const fargateService = new ecsPatterns.NetworkLoadBalancedFargateService(this, "ExpressFargateService", {
            cluster: this.cluster,
            taskDefinition: taskDefinition,
            serviceName: "express-fargate-service",
            publicLoadBalancer: true,
            desiredCount: 2,
            listenerPort: 80,
            domainZone: undefined,
            domainName: undefined,
            platformVersion: ecs.FargatePlatformVersion.LATEST,
            assignPublicIp: true,
            taskSubnets: {
                subnetType: ec2.SubnetType.PUBLIC, // Explicitly use public subnets
            },
            // Critical: Health check grace period for load balancer health checks
            healthCheckGracePeriod: cdk.Duration.seconds(600), // 10 minutes grace period for container startup and stabilization
        });
        // The NetworkLoadBalancedFargateService should automatically configure the target group
        // to route traffic from listenerPort (80) to the container port (3000)
        // But let's explicitly configure the health check and ensure proper routing
        // Configure the target group to use the correct port
        const cfnTargetGroup = fargateService.targetGroup.node
            .defaultChild;
        cfnTargetGroup.port = 3000; // Ensure target group uses container port
        cfnTargetGroup.protocol = "TCP"; // Network Load Balancer uses TCP
        // Configure health check for the target group (HTTP health checks for better detection)
        fargateService.targetGroup.configureHealthCheck({
            protocol: elasticloadbalancingv2.Protocol.HTTP,
            path: "/health",
            port: "3000",
            interval: cdk.Duration.seconds(30),
            timeout: cdk.Duration.seconds(10),
            healthyThresholdCount: 2,
            unhealthyThresholdCount: 3, // 3 consecutive failures to be unhealthy
        });
        // Set deregistration delay for faster deployments
        fargateService.targetGroup.setAttribute("deregistration_delay.timeout_seconds", "30");
        // CRITICAL FIX: Add security group rule to allow inbound traffic on port 3000
        // This is essential for load balancer health checks to reach the ECS tasks
        fargateService.service.connections.allowFromAnyIpv4(ec2.Port.tcp(3000), "Allow inbound HTTP traffic on port 3000 for load balancer health checks and application traffic");
        this.service = fargateService.service;
        this.networkLoadBalancer = fargateService.loadBalancer;
        // Configure deployment settings for more stable rollouts
        const cfnService = this.service.node.defaultChild;
        cfnService.deploymentConfiguration = {
            maximumPercent: 200,
            minimumHealthyPercent: 50,
            deploymentCircuitBreaker: {
                enable: true,
                rollback: true, // Auto-rollback on failure
            },
        };
        // CRITICAL: Configure the service to use mixed capacity (Fargate + Fargate Spot)
        // Remove the default launchType and set capacity provider strategy
        cfnService.launchType = undefined; // Remove fixed launch type
        cfnService.capacityProviderStrategy = [
            {
                capacityProvider: "FARGATE",
                weight: 1,
                base: 1, // At least 1 task on regular Fargate for reliability
            },
            {
                capacityProvider: "FARGATE_SPOT",
                weight: 4, // 80% of tasks on Fargate Spot for cost savings
            },
        ];
        // VPC Link is NOT needed since our NLB is publicly accessible
        // We'll use direct HTTP integration instead
        /**
         * HTTP API Gateway Configuration - Optimized for Microservices
         * Provides 70% cost savings and 2x better performance vs REST API
         * Perfect for microservices architecture with simple proxy needs
         */
        this.httpApi = new apigatewayv2.HttpApi(this, "ExpressAppApiV2", {
            apiName: "Express Fargate API",
            description: "HTTP API Gateway for Express application on ECS Fargate",
            // CORS configuration for HTTP API
            corsPreflight: {
                allowOrigins: ["*"],
                allowMethods: [
                    apigatewayv2.CorsHttpMethod.GET,
                    apigatewayv2.CorsHttpMethod.POST,
                    apigatewayv2.CorsHttpMethod.PUT,
                    apigatewayv2.CorsHttpMethod.DELETE,
                    apigatewayv2.CorsHttpMethod.HEAD,
                    apigatewayv2.CorsHttpMethod.OPTIONS,
                    apigatewayv2.CorsHttpMethod.PATCH,
                ],
                allowHeaders: [
                    "Content-Type",
                    "X-Amz-Date",
                    "Authorization",
                    "X-Api-Key",
                    "X-Amz-Security-Token",
                ],
            },
        });
        /**
         * HTTP API Gateway Integration with Public NLB
         * Direct HTTP integration - no VPC Link needed since NLB is public
         */
        const nlbIntegration = new apigatewayv2Integrations.HttpUrlIntegration("NlbIntegration", `http://${fargateService.loadBalancer.loadBalancerDnsName}`, {
            method: apigatewayv2.HttpMethod.ANY,
        });
        /**
         * HTTP API Gateway Routes
         * Proxy all requests to the backend service
         */
        this.httpApi.addRoutes({
            path: "/{proxy+}",
            methods: [apigatewayv2.HttpMethod.ANY],
            integration: nlbIntegration,
        });
        // Add root path route
        this.httpApi.addRoutes({
            path: "/",
            methods: [apigatewayv2.HttpMethod.ANY],
            integration: nlbIntegration,
        });
        /**
         * HTTP API Gateway Throttling Configuration
         * HTTP API uses different throttling configuration than REST API
         */
        const stage = this.httpApi.addStage("prod", {
            stageName: "prod",
            throttle: {
                burstLimit: 2000,
                rateLimit: 1000, // requests per second
            },
        });
        // Enable logging for the HTTP API stage
        stage.node.addDependency(this.httpApi);
        /**
         * Auto Scaling Configuration
         * Scales the service based on CPU and memory utilization
         * Ensures optimal resource usage and cost efficiency
         */
        const scalableTarget = this.service.autoScaleTaskCount({
            minCapacity: 2,
            maxCapacity: 20, // Maximum tasks to prevent excessive costs
        });
        // CPU-based scaling
        scalableTarget.scaleOnCpuUtilization("CpuScaling", {
            targetUtilizationPercent: 70,
            scaleInCooldown: cdk.Duration.minutes(5),
            scaleOutCooldown: cdk.Duration.minutes(2),
        });
        // Memory-based scaling
        scalableTarget.scaleOnMemoryUtilization("MemoryScaling", {
            targetUtilizationPercent: 80,
            scaleInCooldown: cdk.Duration.minutes(5),
            scaleOutCooldown: cdk.Duration.minutes(2),
        });
        /**
         * ECS Scheduler for Cost Optimization
         * Automatically adjusts service configuration based on Vietnam business hours
         * Business Hours (8 AM - 8 PM VN): 1 Fargate + 1 Spot (2 tasks total)
         * Off Hours (8 PM - 8 AM VN): 1 Spot only (1 task total)
         * Estimated savings: ~25% reduction in compute costs
         */
        this.createEcsScheduler();
        /**
         * CloudFormation Outputs
         * Provides important resource information for external access
         */
        new cdk.CfnOutput(this, "ApiGatewayURL", {
            value: this.httpApi.url ||
                `https://${this.httpApi.httpApiId}.execute-api.${this.region}.amazonaws.com/`,
            description: "HTTP API Gateway URL (primary endpoint)",
            exportName: "ExpressApp-ApiGatewayURL",
        });
        new cdk.CfnOutput(this, "NetworkLoadBalancerDNS", {
            value: this.networkLoadBalancer.loadBalancerDnsName,
            description: "Network Load Balancer DNS name",
            exportName: "ExpressApp-NetworkLoadBalancerDNS",
        });
        new cdk.CfnOutput(this, "ECRRepositoryURI", {
            value: this.repository.repositoryUri,
            description: "ECR Repository URI for container images",
            exportName: "ExpressApp-ECRRepositoryURI",
        });
        new cdk.CfnOutput(this, "ClusterName", {
            value: this.cluster.clusterName,
            description: "ECS Cluster name",
            exportName: "ExpressApp-ClusterName",
        });
        new cdk.CfnOutput(this, "ServiceName", {
            value: this.service.serviceName,
            description: "ECS Service name",
            exportName: "ExpressApp-ServiceName",
        });
        new cdk.CfnOutput(this, "HealthCheckUrl", {
            value: `${this.httpApi.url ||
                `https://${this.httpApi.httpApiId}.execute-api.${this.region}.amazonaws.com/`}health`,
            description: "Application health check URL via HTTP API Gateway",
            exportName: "ExpressApp-HealthCheckUrl",
        });
        // VPC Link not needed - using direct HTTP integration to public NLB
        /**
         * Resource Tagging
         * Apply consistent tags for resource management and cost tracking
         */
        const commonTags = {
            Project: "ExpressFargateApp",
            Environment: props?.tags?.Environment || "Development",
            Service: "ECS-Fargate",
            CostCenter: "Engineering",
            Owner: "DevOps-Team",
        };
        // Apply tags to all resources in the stack
        Object.entries(commonTags).forEach(([key, value]) => {
            cdk.Tags.of(this).add(key, value);
        });
    }
    /**
     * Creates ECS scheduler for cost optimization
     * Automatically adjusts service configuration based on Vietnam business hours
     */
    createEcsScheduler() {
        /**
         * IAM Role for ECS Scheduler Lambda Functions
         * Grants permissions to update ECS service configuration
         */
        const schedulerRole = new iam.Role(this, "EcsSchedulerRole", {
            assumedBy: new iam.ServicePrincipal("lambda.amazonaws.com"),
            description: "Role for ECS scheduler Lambda functions",
            managedPolicies: [
                iam.ManagedPolicy.fromAwsManagedPolicyName("service-role/AWSLambdaBasicExecutionRole"),
            ],
            inlinePolicies: {
                EcsSchedulerPolicy: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            effect: iam.Effect.ALLOW,
                            actions: [
                                "ecs:UpdateService",
                                "ecs:DescribeServices",
                                "ecs:DescribeClusters",
                                "ecs:ListServices",
                                "application-autoscaling:RegisterScalableTarget",
                                "application-autoscaling:DeregisterScalableTarget",
                                "application-autoscaling:DescribeScalableTargets",
                                "application-autoscaling:UpdateScalableTarget",
                            ],
                            resources: [
                                this.cluster.clusterArn,
                                this.service.serviceArn,
                                `arn:aws:application-autoscaling:${this.region}:${this.account}:scalable-target/*`,
                            ],
                        }),
                        new iam.PolicyStatement({
                            effect: iam.Effect.ALLOW,
                            actions: [
                                "logs:CreateLogGroup",
                                "logs:CreateLogStream",
                                "logs:PutLogEvents",
                            ],
                            resources: [
                                `arn:aws:logs:${this.region}:${this.account}:log-group:/aws/lambda/ecs-scheduler-*`,
                            ],
                        }),
                    ],
                }),
            },
        });
        /**
         * Lambda Function for Business Hours Configuration
         * Scales up to 2 tasks (1 Fargate + 1 Spot) during business hours
         */
        const businessHoursFunction = new lambda.Function(this, "BusinessHoursScheduler", {
            runtime: lambda.Runtime.NODEJS_18_X,
            handler: "index.handler",
            role: schedulerRole,
            timeout: cdk.Duration.minutes(5),
            environment: {
                CLUSTER_NAME: this.cluster.clusterName,
                SERVICE_NAME: this.service.serviceName,
                DESIRED_COUNT: "2",
                SCHEDULE_TYPE: "BUSINESS_HOURS",
            },
            code: lambda.Code.fromInline(`
const { ECSClient, UpdateServiceCommand, DescribeServicesCommand } = require("@aws-sdk/client-ecs");
const { ApplicationAutoScalingClient, UpdateScalableTargetCommand } = require("@aws-sdk/client-application-autoscaling");

const ecs = new ECSClient();
const autoscaling = new ApplicationAutoScalingClient();

exports.handler = async (event) => {
  console.log('Starting business hours ECS configuration...');
  console.log('Event:', JSON.stringify(event, null, 2));

  const clusterName = process.env.CLUSTER_NAME;
  const serviceName = process.env.SERVICE_NAME;
  const desiredCount = parseInt(process.env.DESIRED_COUNT);

  try {
    // Business hours configuration: 1 Fargate + 1 Spot
    const updateParams = {
      cluster: clusterName,
      service: serviceName,
      desiredCount: desiredCount,
      capacityProviderStrategy: [
        {
          capacityProvider: "FARGATE",
          weight: 1,
          base: 1  // At least 1 task on Fargate for reliability
        },
        {
          capacityProvider: "FARGATE_SPOT",
          weight: 1  // 1 additional task on Spot
        }
      ]
    };

    console.log('Updating ECS service for business hours:', updateParams);
    const result = await ecs.send(new UpdateServiceCommand(updateParams));

    // Update auto-scaling min capacity for business hours
    try {
      await autoscaling.send(new UpdateScalableTargetCommand({
        ServiceNamespace: 'ecs',
        ResourceId: \`service/\${clusterName}/\${serviceName}\`,
        ScalableDimension: 'ecs:service:DesiredCount',
        MinCapacity: 2,  // Minimum 2 tasks during business hours
        MaxCapacity: 20
      }));
      console.log('Updated auto-scaling min capacity to 2 for business hours');
    } catch (scalingError) {
      console.warn('Failed to update auto-scaling target:', scalingError.message);
    }

    console.log('Business hours configuration completed successfully');
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Business hours ECS configuration applied successfully',
        desiredCount: desiredCount,
        capacityStrategy: 'Mixed (Fargate + Spot)',
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Error updating ECS service:', error);
    throw error;
  }
};
        `),
        });
        /**
         * Lambda Function for Off-Hours Configuration
         * Scales down to 1 Spot task only during off-hours
         */
        const offHoursFunction = new lambda.Function(this, "OffHoursScheduler", {
            runtime: lambda.Runtime.NODEJS_18_X,
            handler: "index.handler",
            role: schedulerRole,
            timeout: cdk.Duration.minutes(5),
            environment: {
                CLUSTER_NAME: this.cluster.clusterName,
                SERVICE_NAME: this.service.serviceName,
                DESIRED_COUNT: "1",
                SCHEDULE_TYPE: "OFF_HOURS",
            },
            code: lambda.Code.fromInline(`
const { ECSClient, UpdateServiceCommand, DescribeServicesCommand } = require("@aws-sdk/client-ecs");
const { ApplicationAutoScalingClient, UpdateScalableTargetCommand } = require("@aws-sdk/client-application-autoscaling");

const ecs = new ECSClient();
const autoscaling = new ApplicationAutoScalingClient();

exports.handler = async (event) => {
  console.log('Starting off-hours ECS configuration...');
  console.log('Event:', JSON.stringify(event, null, 2));

  const clusterName = process.env.CLUSTER_NAME;
  const serviceName = process.env.SERVICE_NAME;
  const desiredCount = parseInt(process.env.DESIRED_COUNT);

  try {
    // Off-hours configuration: 1 Spot task only
    const updateParams = {
      cluster: clusterName,
      service: serviceName,
      desiredCount: desiredCount,
      capacityProviderStrategy: [
        {
          capacityProvider: "FARGATE_SPOT",
          weight: 1,
          base: 1  // 1 task on Spot for cost optimization
        }
      ]
    };

    console.log('Updating ECS service for off-hours:', updateParams);
    const result = await ecs.send(new UpdateServiceCommand(updateParams));

    // Update auto-scaling min capacity for off-hours
    try {
      await autoscaling.send(new UpdateScalableTargetCommand({
        ServiceNamespace: 'ecs',
        ResourceId: \`service/\${clusterName}/\${serviceName}\`,
        ScalableDimension: 'ecs:service:DesiredCount',
        MinCapacity: 1,  // Minimum 1 task during off-hours
        MaxCapacity: 20
      }));
      console.log('Updated auto-scaling min capacity to 1 for off-hours');
    } catch (scalingError) {
      console.warn('Failed to update auto-scaling target:', scalingError.message);
    }

    console.log('Off-hours configuration completed successfully');
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Off-hours ECS configuration applied successfully',
        desiredCount: desiredCount,
        capacityStrategy: 'Spot only',
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Error updating ECS service:', error);
    throw error;
  }
};
      `),
        });
        /**
         * EventBridge Rules for Vietnam Timezone Scheduling
         * Business hours: 8 AM Vietnam time (1 AM UTC)
         * Off hours: 8 PM Vietnam time (1 PM UTC)
         * Vietnam is UTC+7
         */
        // Business hours rule: Trigger at 8 AM Vietnam time (1 AM UTC)
        const businessHoursRule = new events.Rule(this, "BusinessHoursRule", {
            description: "Trigger business hours ECS configuration at 8 AM Vietnam time",
            schedule: events.Schedule.cron({
                minute: "0",
                hour: "1",
                day: "*",
                month: "*",
                year: "*",
            }),
        });
        businessHoursRule.addTarget(new targets.LambdaFunction(businessHoursFunction));
        // Off-hours rule: Trigger at 8 PM Vietnam time (1 PM UTC)
        const offHoursRule = new events.Rule(this, "OffHoursRule", {
            description: "Trigger off-hours ECS configuration at 8 PM Vietnam time",
            schedule: events.Schedule.cron({
                minute: "0",
                hour: "13",
                day: "*",
                month: "*",
                year: "*",
            }),
        });
        offHoursRule.addTarget(new targets.LambdaFunction(offHoursFunction));
        /**
         * CloudWatch Log Groups for Scheduler Functions
         */
        new logs.LogGroup(this, "BusinessHoursSchedulerLogGroup", {
            logGroupName: `/aws/lambda/${businessHoursFunction.functionName}`,
            retention: logs.RetentionDays.ONE_WEEK,
            removalPolicy: cdk.RemovalPolicy.DESTROY,
        });
        new logs.LogGroup(this, "OffHoursSchedulerLogGroup", {
            logGroupName: `/aws/lambda/${offHoursFunction.functionName}`,
            retention: logs.RetentionDays.ONE_WEEK,
            removalPolicy: cdk.RemovalPolicy.DESTROY,
        });
        /**
         * CloudFormation Outputs for Scheduler
         */
        new cdk.CfnOutput(this, "BusinessHoursSchedulerArn", {
            value: businessHoursFunction.functionArn,
            description: "Business hours scheduler Lambda function ARN",
            exportName: "ExpressApp-BusinessHoursSchedulerArn",
        });
        new cdk.CfnOutput(this, "OffHoursSchedulerArn", {
            value: offHoursFunction.functionArn,
            description: "Off-hours scheduler Lambda function ARN",
            exportName: "ExpressApp-OffHoursSchedulerArn",
        });
        new cdk.CfnOutput(this, "SchedulerInfo", {
            value: JSON.stringify({
                businessHours: "8 AM - 8 PM Vietnam (1 AM - 1 PM UTC)",
                businessConfig: "2 tasks (1 Fargate + 1 Spot)",
                offHoursConfig: "1 task (Spot only)",
                estimatedSavings: "~25% compute cost reduction",
            }),
            description: "ECS scheduler configuration summary",
            exportName: "ExpressApp-SchedulerInfo",
        });
    }
}
exports.EcsFargateAndSpotStack = EcsFargateAndSpotStack;
//# sourceMappingURL=data:application/json;base64,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