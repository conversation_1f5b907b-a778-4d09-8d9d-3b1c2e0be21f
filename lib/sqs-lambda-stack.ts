import * as cdk from "aws-cdk-lib";
import * as lambda from "aws-cdk-lib/aws-lambda";
import * as sqs from "aws-cdk-lib/aws-sqs";
import * as lambdaEventSources from "aws-cdk-lib/aws-lambda-event-sources";
import * as iam from "aws-cdk-lib/aws-iam";
import * as logs from "aws-cdk-lib/aws-logs";
import { Construct } from "constructs";

/**
 * CDK Stack that creates an SQS queue and Lambda function integration
 * The Lambda function will be triggered whenever messages are sent to the SQS queue
 */
export class SqsLambdaStack extends cdk.Stack {
  public readonly queue: sqs.Queue;
  public readonly lambdaFunction: lambda.Function;

  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Create SQS Queue with Dead Letter Queue for failed message handling
    const deadLetterQueue = new sqs.Queue(this, "ProcessingDeadLetterQueue", {
      queueName: "sqs-lambda-dlq",
      retentionPeriod: cdk.Duration.days(14), // Keep failed messages for 14 days
    });

    // Main SQS Queue that will trigger the Lambda function
    this.queue = new sqs.Queue(this, "ProcessingQueue", {
      queueName: "sqs-lambda-processing-queue",
      visibilityTimeout: cdk.Duration.seconds(300), // 5 minutes - should be >= Lambda timeout
      retentionPeriod: cdk.Duration.days(7), // Keep messages for 7 days
      deadLetterQueue: {
        queue: deadLetterQueue,
        maxReceiveCount: 3, // Retry failed messages 3 times before moving to DLQ
      },
    });

    // Create CloudWatch Log Group for Lambda function logs
    const logGroup = new logs.LogGroup(this, "LambdaLogGroup", {
      logGroupName: "/aws/lambda/sqs-message-processor",
      retention: logs.RetentionDays.ONE_WEEK,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // For demo purposes
    });

    // Create Lambda function that processes SQS messages
    this.lambdaFunction = new lambda.Function(this, "MessageProcessor", {
      functionName: "sqs-message-processor",
      runtime: lambda.Runtime.NODEJS_18_X, // Use latest LTS Node.js runtime
      handler: "index.handler",
      code: lambda.Code.fromAsset("lambda"), // Lambda code will be in ./lambda directory
      timeout: cdk.Duration.seconds(30), // Set timeout for message processing
      memorySize: 256, // Sufficient memory for basic message processing
      // reservedConcurrentExecutions: 10, // Commented out due to account limits - test natural scaling instead
      environment: {
        QUEUE_URL: this.queue.queueUrl,
        NODE_ENV: "production",
      },
      description: "Processes messages from SQS queue and logs their content",
    });

    // Grant Lambda function permissions to consume messages from the SQS queue
    this.queue.grantConsumeMessages(this.lambdaFunction);

    // Add SQS as an event source for the Lambda function
    // This creates the necessary event source mapping
    this.lambdaFunction.addEventSource(
      new lambdaEventSources.SqsEventSource(this.queue, {
        batchSize: 10, // Process up to 10 messages at once
        maxBatchingWindow: cdk.Duration.seconds(5), // Wait up to 5 seconds to collect messages
        reportBatchItemFailures: true, // Enable partial batch failure reporting
      })
    );

    // Grant Lambda additional permissions for CloudWatch Logs (explicit for clarity)
    this.lambdaFunction.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
        ],
        resources: [logGroup.logGroupArn],
      })
    );

    // Output important resource ARNs and URLs for reference
    new cdk.CfnOutput(this, "QueueUrl", {
      value: this.queue.queueUrl,
      description: "URL of the SQS queue for sending test messages",
      exportName: "SqsLambdaQueueUrl",
    });

    new cdk.CfnOutput(this, "QueueArn", {
      value: this.queue.queueArn,
      description: "ARN of the SQS queue",
      exportName: "SqsLambdaQueueArn",
    });

    new cdk.CfnOutput(this, "LambdaFunctionArn", {
      value: this.lambdaFunction.functionArn,
      description: "ARN of the Lambda function",
      exportName: "SqsLambdaFunctionArn",
    });

    new cdk.CfnOutput(this, "DeadLetterQueueUrl", {
      value: deadLetterQueue.queueUrl,
      description: "URL of the Dead Letter Queue for failed messages",
      exportName: "SqsLambdaDeadLetterQueueUrl",
    });
  }
}
