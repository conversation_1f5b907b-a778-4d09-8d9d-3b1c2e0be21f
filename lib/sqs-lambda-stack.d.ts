import * as cdk from "aws-cdk-lib";
import * as lambda from "aws-cdk-lib/aws-lambda";
import * as sqs from "aws-cdk-lib/aws-sqs";
import { Construct } from "constructs";
/**
 * CDK Stack that creates an SQS queue and Lambda function integration
 * The Lambda function will be triggered whenever messages are sent to the SQS queue
 */
export declare class SqsLambdaStack extends cdk.Stack {
    readonly queue: sqs.Queue;
    readonly lambdaFunction: lambda.Function;
    constructor(scope: Construct, id: string, props?: cdk.StackProps);
}
