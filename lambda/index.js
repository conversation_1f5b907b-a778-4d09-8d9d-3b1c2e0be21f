/**
 * AWS Lambda function that processes messages from SQS queue
 * This function is triggered automatically when messages are available in the SQS queue
 */

/**
 * Main Lambda handler function
 * Processes SQS messages and performs business logic
 *
 * @param {Object} event - SQS event containing message records
 * @param {Object} context - Lambda runtime context
 * @returns {Promise<Object>} - Processing results with batch item failures if any
 */
exports.handler = async (event, context) => {
  console.log("Lambda function started processing SQS messages");
  console.log(`Processing ${event.Records.length} message(s)`);

  // Array to collect failed message processing results
  const batchItemFailures = [];

  // Process each SQS message record
  for (const record of event.Records) {
    try {
      console.log(`Processing message ID: ${record.messageId}`);
      console.log(`Message body: ${record.body}`);
      console.log(
        `Message attributes:`,
        JSON.stringify(record.messageAttributes, null, 2)
      );

      // Parse message body if it's JSON
      let messageData;
      try {
        messageData = JSON.parse(record.body);
        console.log(
          "Parsed message data:",
          JSON.stringify(messageData, null, 2)
        );
      } catch (parseError) {
        console.log("Message body is not valid JSON, treating as plain text");
        messageData = { rawMessage: record.body };
      }

      // Simulate business logic processing
      await processMessage(messageData, record);

      console.log(`Successfully processed message ID: ${record.messageId}`);
    } catch (error) {
      console.error(`Error processing message ID ${record.messageId}:`, error);

      // Add failed message to batch item failures for retry
      batchItemFailures.push({
        itemIdentifier: record.messageId,
      });
    }
  }

  console.log(
    `Processing completed. ${batchItemFailures.length} message(s) failed`
  );

  // Return batch item failures for partial batch failure handling
  // SQS will retry only the failed messages
  return {
    batchItemFailures: batchItemFailures,
  };
};

/**
 * Business logic for processing individual messages
 * Add your custom message processing logic here
 *
 * @param {Object} messageData - Parsed message data
 * @param {Object} record - Original SQS record
 * @returns {Promise<void>}
 */
async function processMessage(messageData, record) {
  console.log("Starting business logic processing...");

  // Example processing based on message content
  if (messageData.action) {
    switch (messageData.action.toLowerCase()) {
      case "process_order":
        await processOrder(messageData);
        break;
      case "send_notification":
        await sendNotification(messageData);
        break;
      case "update_inventory":
        await updateInventory(messageData);
        break;
      default:
        console.log(
          `Unknown action: ${messageData.action}, processing as generic message`
        );
        await processGenericMessage(messageData);
    }
  } else {
    // Process messages without specific action
    await processGenericMessage(messageData);
  }

  // Simulate processing time to make concurrency effects more visible
  const processingTime = messageData.processingTime
    ? messageData.processingTime * 1000
    : 2000;
  await sleep(processingTime);

  console.log(`Business logic processing completed (took ${processingTime}ms)`);
}

/**
 * Process order-related messages
 * @param {Object} messageData - Message containing order information
 */
async function processOrder(messageData) {
  console.log("Processing order:", messageData.orderId || "Unknown order ID");
  // Add your order processing logic here
  // Example: validate order, update database, trigger fulfillment
}

/**
 * Send notification based on message content
 * @param {Object} messageData - Message containing notification details
 */
async function sendNotification(messageData) {
  console.log(
    "Sending notification to:",
    messageData.recipient || "Unknown recipient"
  );
  // Add your notification logic here
  // Example: send email, SMS, push notification
}

/**
 * Update inventory based on message content
 * @param {Object} messageData - Message containing inventory update details
 */
async function updateInventory(messageData) {
  console.log(
    "Updating inventory for product:",
    messageData.productId || "Unknown product"
  );
  // Add your inventory update logic here
  // Example: update database, trigger reorder alerts
}

/**
 * Process generic messages that don't have specific actions
 * @param {Object} messageData - Generic message data
 */
async function processGenericMessage(messageData) {
  console.log(
    "Processing generic message with data keys:",
    Object.keys(messageData)
  );
  // Add your generic processing logic here
  // Example: log to database, trigger analytics
}

/**
 * Utility function to simulate async processing time
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
