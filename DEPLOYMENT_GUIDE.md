# ECS Fargate Deployment Guide

This guide explains how to build and deploy the Express application to AWS ECS Fargate using CDK.

## Prerequisites

Before deploying, ensure you have:

1. **AWS CLI** installed and configured with appropriate credentials
2. **Docker** installed and running
3. **Node.js** and **npm** installed
4. **AWS CDK** installed globally: `npm install -g aws-cdk`
5. **Proper AWS permissions** for ECR, ECS, VPC, IAM, and CloudFormation

## Quick Start

### 1. Complete Deployment (Recommended)

This will build the Docker image, push it to ECR, and deploy the ECS stack:

```bash
# For development deployment
npm run deploy:full

# For production deployment
npm run deploy:full-prod
```

### 2. Step-by-Step Deployment

If you prefer to run each step manually:

```bash
# Step 1: Build and push Docker image to ECR
npm run docker:build-and-push

# Step 2: Deploy the ECS Fargate stack
npm run deploy:ecs
```

## Understanding the Deployment Process

### Docker Image Build and Push

The deployment process includes these steps:

1. **Express App Build**: Compiles TypeScript to JavaScript
2. **Docker Image Build**: Creates a production-ready container image
3. **ECR Authentication**: Authenticates Docker with AWS ECR
4. **ECR Repository Creation**: Creates the repository if it doesn't exist
5. **Image Push**: Pushes the image to ECR with appropriate tags

### ECS Stack Deployment

The CDK stack creates:

- **VPC** with public subnets (cost-optimized, no NAT Gateway)
- **ECR Repository** for container images
- **ECS Cluster** with Fargate and Fargate Spot capacity providers
- **ECS Service** with auto-scaling capabilities
- **Network Load Balancer** for high performance
- **HTTP API Gateway** for external access
- **CloudWatch Logs** for monitoring

## Available Commands

### Docker Commands

```bash
# Build Docker image locally
npm run docker:build

# Build and push image to ECR (latest tag)
npm run docker:build-and-push

# Build and push image to ECR (prod tag)
npm run docker:build-and-push-prod
```

### CDK Commands

```bash
# Deploy only the ECS stack
npm run deploy:ecs

# Synthesize CloudFormation template
npm run synth

# Destroy the ECS stack
cdk destroy EcsFargateAndSpotStack
```

### Manual Script Usage

You can also run the build script directly with custom parameters:

```bash
# Basic usage (uses 'latest' tag and 'ap-southeast-1' region)
./scripts/build-and-push-image.sh

# With custom tag
./scripts/build-and-push-image.sh v1.2.3

# With custom tag and region
./scripts/build-and-push-image.sh v1.2.3 ap-southeast-1
```

## Troubleshooting

### Common Issues

1. **"No images found in ECR repository"**

   - **Solution**: Run `npm run docker:build-and-push` before deploying

2. **"Docker daemon not running"**

   - **Solution**: Start Docker Desktop or Docker service

3. **"AWS credentials not configured"**

   - **Solution**: Run `aws configure` or set AWS environment variables

4. **"Permission denied: ./scripts/build-and-push-image.sh"**

   - **Solution**: Run `chmod +x scripts/build-and-push-image.sh`

5. **"Repository does not exist"**
   - **Solution**: The script will automatically create the ECR repository

### Debugging

1. **View ECS Service Status**:

   ```bash
   aws ecs describe-services --cluster express-fargate-cluster --services express-fargate-service
   ```

2. **View ECS Task Logs**:

   ```bash
   aws logs tail /ecs/express-fargate-app --follow
   ```

3. **View API Gateway URL**:
   ```bash
   aws cloudformation describe-stacks --stack-name EcsFargateAndSpotStack --query 'Stacks[0].Outputs'
   ```

## Architecture Overview

The deployed architecture includes:

- **Cost Optimization**: Uses public subnets to avoid NAT Gateway costs (~$90/month savings)
- **High Availability**: Deploys across multiple Availability Zones
- **Auto Scaling**: Scales based on CPU and memory utilization
- **Mixed Capacity**: Uses both Fargate (reliable) and Fargate Spot (cost-effective)
- **Modern API**: HTTP API Gateway provides 70% cost savings vs REST API
- **Security**: Non-root container user, image vulnerability scanning

## Monitoring and Logs

After deployment, you can monitor your application through:

1. **CloudWatch Logs**: `/ecs/express-fargate-app`
2. **ECS Console**: View service status, tasks, and metrics
3. **API Gateway Console**: Monitor API requests and performance
4. **CloudWatch Metrics**: CPU, memory, and request metrics

## Cost Optimization Features

- **No NAT Gateways**: Uses public subnets with public IPs
- **Fargate Spot**: 80% of tasks run on cost-effective Spot capacity
- **HTTP API Gateway**: 70% cheaper than REST API Gateway
- **Image Lifecycle**: Automatic cleanup of old images
- **Resource Tagging**: For cost tracking and management

## Next Steps

After successful deployment:

1. Test your application using the API Gateway URL from the CDK outputs
2. Set up monitoring and alerting
3. Configure custom domains if needed
4. Implement CI/CD pipeline for automated deployments
5. Configure environment-specific settings

## Support

If you encounter issues:

1. Check the deployment logs
2. Verify AWS credentials and permissions
3. Ensure Docker is running
4. Check the troubleshooting section above
