# Lambda Concurrency Testing Guide

This guide helps you test Lambda concurrency limits and scaling behavior with SQS message processing.

## 🎯 **Test Setup Overview**

- **Lambda Reserved Concurrency**: **No limit** (test natural AWS scaling behavior)
- **SQS Batch Size**: Up to 10 messages per invocation
- **Message Processing Time**: 1-10 seconds (configurable per message)
- **Queue Visibility Timeout**: 5 minutes

## 🚀 **Quick Start Testing**

### 1. **Deploy the Stack**

```bash
npm run build
npm run deploy
```

### 2. **Monitor Lambda Processing** (Terminal 1)

```bash
./scripts/monitor-processing.sh
```

### 3. **Send Test Messages** (Terminal 2)

```bash
# Send 50 messages in 5 concurrent batches
./scripts/send-multiple-messages.sh 50

# Send 100 messages in 10 concurrent batches
./scripts/send-multiple-messages.sh 100 "" 10

# Send 200 messages to really test throttling
./scripts/send-multiple-messages.sh 200
```

### 4. **Watch Lambda Logs** (Terminal 3)

```bash
aws logs tail /aws/lambda/sqs-message-processor --follow
```

## 📊 **What You'll Observe**

### **Normal Processing (Low Load)**

- Messages processed immediately
- Concurrency: Scales based on message volume (up to account limits)
- No throttling

### **High Load Testing (50+ Messages)**

- **Natural Scaling**: Lambda scales up to handle message volume
- **Account Limits**: May hit account-level concurrent execution limits
- **Throttling**: Only if account limits are exceeded
- **Rapid Processing**: Messages processed quickly with natural scaling

### **Expected Behavior Timeline**

1. **0-10s**: Rapid message sending
2. **10-30s**: Lambda scaling up naturally based on message volume
3. **30s+**: Steady processing at scale (up to account limits)
4. **Queue drain**: Gradual processing until empty

## 🔍 **Monitoring Commands**

### **Queue Status**

```bash
# Quick queue check
aws sqs get-queue-attributes \
  --queue-url YOUR_QUEUE_URL \
  --attribute-names ApproximateNumberOfMessages,ApproximateNumberOfMessagesNotVisible

# Watch queue in real-time
watch -n 2 'aws sqs get-queue-attributes --queue-url YOUR_QUEUE_URL --attribute-names ApproximateNumberOfMessages,ApproximateNumberOfMessagesNotVisible --output table'
```

### **Lambda Metrics**

```bash
# Check Lambda metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/Lambda \
  --metric-name ConcurrentExecutions \
  --dimensions Name=FunctionName,Value=sqs-message-processor \
  --start-time $(date -u -d '10 minutes ago' +%Y-%m-%dT%H:%M:%S) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
  --period 60 \
  --statistics Maximum

# Check for throttles
aws cloudwatch get-metric-statistics \
  --namespace AWS/Lambda \
  --metric-name Throttles \
  --dimensions Name=FunctionName,Value=sqs-message-processor \
  --start-time $(date -u -d '10 minutes ago' +%Y-%m-%dT%H:%M:%S) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
  --period 300 \
  --statistics Sum
```

## 🧪 **Test Scenarios**

### **Scenario 1: Basic Concurrency Test**

```bash
# Send moderate load
./scripts/send-multiple-messages.sh 30

# Expected: Natural Lambda scaling, processes quickly
```

### **Scenario 2: Throttling Test**

```bash
# Send high load
./scripts/send-multiple-messages.sh 100

# Expected: Throttling occurs, queue builds up
```

### **Scenario 3: Sustained Load Test**

```bash
# Send messages continuously
for i in {1..5}; do
  ./scripts/send-multiple-messages.sh 20
  sleep 10
done

# Expected: Consistent processing at concurrency limit
```

### **Scenario 4: Different Message Types**

The script automatically sends different message types:

- `process_order` - Takes 1-10 seconds to process
- `send_notification` - Quick processing
- `update_inventory` - Medium processing time
- `generic_task` - Variable processing time

## 📈 **Key Metrics to Watch**

### **SQS Metrics**

- **ApproximateNumberOfMessages**: Pending messages
- **ApproximateNumberOfMessagesNotVisible**: In-flight messages
- **Message Age**: How long messages wait

### **Lambda Metrics**

- **ConcurrentExecutions**: Current running instances
- **Throttles**: Rejected invocations due to concurrency limits
- **Duration**: How long each invocation takes
- **Errors**: Failed message processing

### **CloudWatch Alarms** (Optional Setup)

```bash
# Create alarm for high queue depth
aws cloudwatch put-metric-alarm \
  --alarm-name "SQS-Queue-Depth-High" \
  --alarm-description "Alert when queue has >50 messages" \
  --metric-name ApproximateNumberOfVisibleMessages \
  --namespace AWS/SQS \
  --statistic Average \
  --period 300 \
  --threshold 50 \
  --comparison-operator GreaterThanThreshold \
  --dimensions Name=QueueName,Value=sqs-lambda-processing-queue

# Create alarm for Lambda throttles
aws cloudwatch put-metric-alarm \
  --alarm-name "Lambda-Throttles-High" \
  --alarm-description "Alert when Lambda is throttling" \
  --metric-name Throttles \
  --namespace AWS/Lambda \
  --statistic Sum \
  --period 300 \
  --threshold 1 \
  --comparison-operator GreaterThanOrEqualToThreshold \
  --dimensions Name=FunctionName,Value=sqs-message-processor
```

## 🔧 **Tuning Concurrency**

### **Increase Concurrency** (in `lib/sqs-lambda-stack.ts`)

```typescript
reservedConcurrentExecutions: 20, // Increase from 10 to 20
```

### **Remove Concurrency Limit**

```typescript
// Comment out or remove this line
// reservedConcurrentExecutions: 10,
```

### **Set Account-Level Limits**

```bash
# Set account concurrent execution limit
aws lambda put-account-settings --max-concurrent-executions 100
```

## 🔍 **Troubleshooting**

### **Messages Not Processing**

1. Check Lambda function logs for errors
2. Verify IAM permissions
3. Check dead letter queue for failed messages

### **Slow Processing**

1. Increase Lambda memory (affects CPU)
2. Optimize your message processing logic
3. Consider parallel processing within Lambda

### **High Costs**

1. Reduce message processing time
2. Lower concurrency limits
3. Use SQS message batching effectively

## 💡 **Advanced Testing**

### **Custom Message Processing Times**

Send messages with specific processing times:

```bash
./scripts/send-test-message.sh '{"action":"process_order","processingTime":5,"orderId":"test-123"}'
```

### **Load Testing with Artillery** (Optional)

Create `load-test.yml`:

```yaml
config:
  target: "http://your-api-gateway-endpoint"
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Send SQS Messages"
    flow:
      - post:
          url: "/send-message"
          json:
            message: "Load test message"
```

## 📝 **Results Documentation**

Document your test results:

- Peak concurrent executions achieved
- Throttling thresholds
- Queue processing rates
- Cost implications
- Performance bottlenecks

This will help you optimize your production workloads!
