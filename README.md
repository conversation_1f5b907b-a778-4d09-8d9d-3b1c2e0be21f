# SQS Lambda CDK Integration

A simple AWS CDK project demonstrating how to set up an SQS queue that triggers a Lambda function for message processing.

## Architecture

```
SQS Queue → Lambda Function → CloudWatch Logs
    ↓
Dead Letter Queue (for failed messages)
```

## Features

- **SQS Queue**: Main processing queue with dead letter queue for failed messages
- **Lambda Function**: Processes SQS messages with comprehensive logging
- **Error Handling**: Partial batch failure support and retry mechanism
- **Monitoring**: CloudWatch Logs integration for debugging and monitoring
- **Test Scripts**: Includes utilities for testing the integration

## Prerequisites

- Node.js 18+ and npm
- AWS CLI configured with appropriate permissions
- AWS CDK CLI installed globally: `npm install -g aws-cdk`

## Installation

1. **Install dependencies**:

   ```bash
   npm install
   ```

2. **Bootstrap CDK** (if not done before in your AWS account/region):

   ```bash
   npx cdk bootstrap
   ```

3. **Build the project**:
   ```bash
   npm run build
   ```

## Deployment

1. **Deploy the stack**:

   ```bash
   npm run deploy
   ```

2. **Note the outputs**: After deployment, save the queue URL and other outputs for testing.

## Testing

### Method 1: Using the Test Script

1. **Make the script executable**:

   ```bash
   chmod +x scripts/send-test-message.sh
   ```

2. **Send a simple message**:

   ```bash
   ./scripts/send-test-message.sh "Hello from SQS!"
   ```

3. **Send a structured message**:
   ```bash
   ./scripts/send-test-message.sh '{"action":"process_order","orderId":"ORD-123","customerId":"CUST-456"}'
   ```

### Method 2: Using AWS CLI

1. **Get the queue URL from CDK outputs or AWS Console**

2. **Send a message**:
   ```bash
   aws sqs send-message \\
     --queue-url YOUR_QUEUE_URL \\
     --message-body "Test message from AWS CLI"
   ```

### Method 3: Using AWS Console

1. Go to AWS SQS Console
2. Find your queue: `sqs-lambda-processing-queue`
3. Click "Send and receive messages"
4. Enter your test message and click "Send message"

## Monitoring

### View Lambda Logs

```bash
# Real-time log streaming
aws logs tail /aws/lambda/sqs-message-processor --follow

# View recent logs
aws logs describe-log-streams --log-group-name /aws/lambda/sqs-message-processor
```

### Check Queue Status

```bash
# Get queue attributes
aws sqs get-queue-attributes \\
  --queue-url YOUR_QUEUE_URL \\
  --attribute-names All

# Check for messages in dead letter queue
aws sqs get-queue-attributes \\
  --queue-url YOUR_DLQ_URL \\
  --attribute-names ApproximateNumberOfMessages
```

## Sample Messages

The `test-messages.json` file contains various message examples:

- Simple text messages
- Order processing messages
- Notification messages
- Inventory updates
- Error-inducing messages (for testing failure handling)

## Lambda Function Details

The Lambda function (`lambda/index.js`) includes:

- **Message Processing**: Handles different message types based on `action` field
- **Error Handling**: Catches and logs errors, supports partial batch failures
- **Logging**: Comprehensive logging for debugging and monitoring
- **Business Logic**: Example processing for orders, notifications, and inventory

## Configuration

### Key Parameters

- **Batch Size**: 10 messages (configurable in `sqs-lambda-stack.ts`)
- **Visibility Timeout**: 5 minutes
- **Dead Letter Queue**: 3 retry attempts before moving to DLQ
- **Lambda Timeout**: 30 seconds
- **Log Retention**: 1 week

### Environment Variables

The Lambda function has access to:

- `QUEUE_URL`: The SQS queue URL
- `NODE_ENV`: Set to 'production'

## Cleanup

To remove all resources:

```bash
npm run destroy
```

## Troubleshooting

### Common Issues

1. **Lambda not triggering**: Check IAM permissions and event source mapping
2. **Messages in DLQ**: Check Lambda logs for processing errors
3. **Permission denied**: Ensure AWS CLI is configured with proper permissions

### Useful Commands

```bash
# List all stacks
npx cdk list

# View stack template
npx cdk synth

# Compare deployed stack with local changes
npx cdk diff

# View CloudFormation events
aws cloudformation describe-stack-events --stack-name SqsLambdaStack
```

## Cost Considerations

This setup uses AWS free tier eligible services:

- SQS: 1 million requests per month free
- Lambda: 1 million requests and 400,000 GB-seconds per month free
- CloudWatch Logs: 5 GB ingestion and storage per month free

## Security Best Practices

- IAM roles follow principle of least privilege
- No hardcoded credentials
- CloudWatch Logs for audit trails
- Dead letter queue for failed message analysis

## Next Steps

- Add more sophisticated message processing logic
- Implement message filtering and routing
- Add metrics and alarms for monitoring
- Consider adding SNS for fan-out patterns
- Implement message deduplication for FIFO queues
