const https = require("https");
const http = require("http");
const winston = require("winston");

/**
 * Autoscaling Test Script for Express Fargate Application
 * Tests the autoscaling behavior of the ECS Fargate service by gradually increasing load
 * and monitoring response times, success rates, and scaling events.
 */

// Winston logger configuration for comprehensive logging
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: "autoscaling-test" },
  transports: [
    // Write all logs to console with colors
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
    // Write all logs to file
    new winston.transports.File({
      filename: "../logs/autoscaling-test.log",
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
    }),
  ],
});

/**
 * Configuration object for autoscaling test parameters
 */
const testConfig = {
  // Target endpoint for testing
  endpoint: String(
    "http://EcsFar-Expre-Q67zKZK54Irb-3cfb67497e94bbd3.elb.ap-southeast-1.amazonaws.com/"
  ).toLowerCase(),

  // Test phases configuration
  phases: [
    { name: "warmup", duration: 30000, concurrency: 1, rampUp: false },
    { name: "light-load", duration: 60000, concurrency: 5, rampUp: true },
    { name: "medium-load", duration: 120000, concurrency: 20, rampUp: true },
    { name: "heavy-load", duration: 180000, concurrency: 50, rampUp: true },
    { name: "peak-load", duration: 300000, concurrency: 100, rampUp: true },
    { name: "cool-down", duration: 60000, concurrency: 5, rampUp: false },
  ],

  // Request configuration
  requestTimeout: 30000,
  requestInterval: 100, // Base interval between requests in ms

  // Metrics collection interval
  metricsInterval: 5000,

  // Health check endpoints to test
  endpoints: {
    root: "/",
    health: "/health",
    deepHealth: "/health/deep",
    status: "/status",
  },
};

/**
 * Metrics tracking object for performance analysis
 */
class MetricsTracker {
  constructor() {
    this.reset();
  }

  /**
   * Reset all metrics to initial state
   */
  reset() {
    this.totalRequests = 0;
    this.successfulRequests = 0;
    this.failedRequests = 0;
    this.timeouts = 0;
    this.responseTimes = [];
    this.statusCodes = {};
    this.errors = {};
    this.startTime = Date.now();
  }

  /**
   * Record a successful request with response time and status code
   * @param {number} responseTime - Response time in milliseconds
   * @param {number} statusCode - HTTP status code
   */
  recordSuccess(responseTime, statusCode) {
    this.totalRequests++;
    this.successfulRequests++;
    this.responseTimes.push(responseTime);
    this.statusCodes[statusCode] = (this.statusCodes[statusCode] || 0) + 1;

    logger.debug("Request successful", {
      responseTime,
      statusCode,
      totalRequests: this.totalRequests,
    });
  }

  /**
   * Record a failed request with error information
   * @param {Error} error - Error object from failed request
   */
  recordFailure(error) {
    this.totalRequests++;
    this.failedRequests++;

    if (error.code === "TIMEOUT") {
      this.timeouts++;
    }

    const errorType = error.code || error.message || "unknown";
    this.errors[errorType] = (this.errors[errorType] || 0) + 1;

    logger.warn("Request failed", {
      error: error.message,
      code: error.code,
      totalRequests: this.totalRequests,
    });
  }

  /**
   * Get comprehensive metrics summary
   * @returns {Object} Metrics summary object
   */
  getSummary() {
    const duration = Date.now() - this.startTime;
    const avgResponseTime =
      this.responseTimes.length > 0
        ? this.responseTimes.reduce((a, b) => a + b, 0) /
          this.responseTimes.length
        : 0;

    // Calculate percentiles for response time analysis
    const sortedTimes = [...this.responseTimes].sort((a, b) => a - b);
    const p50 = this.getPercentile(sortedTimes, 50);
    const p95 = this.getPercentile(sortedTimes, 95);
    const p99 = this.getPercentile(sortedTimes, 99);

    return {
      duration: duration,
      totalRequests: this.totalRequests,
      successfulRequests: this.successfulRequests,
      failedRequests: this.failedRequests,
      timeouts: this.timeouts,
      successRate:
        this.totalRequests > 0
          ? (this.successfulRequests / this.totalRequests) * 100
          : 0,
      requestsPerSecond: this.totalRequests / (duration / 1000),
      avgResponseTime: Math.round(avgResponseTime),
      p50ResponseTime: Math.round(p50),
      p95ResponseTime: Math.round(p95),
      p99ResponseTime: Math.round(p99),
      minResponseTime: Math.min(...this.responseTimes) || 0,
      maxResponseTime: Math.max(...this.responseTimes) || 0,
      statusCodes: this.statusCodes,
      errors: this.errors,
    };
  }

  /**
   * Calculate percentile value from sorted array
   * @param {Array} sortedArray - Sorted array of numbers
   * @param {number} percentile - Percentile to calculate (0-100)
   * @returns {number} Percentile value
   */
  getPercentile(sortedArray, percentile) {
    if (sortedArray.length === 0) return 0;

    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[Math.max(0, index)];
  }
}

/**
 * HTTP/HTTPS request handler for making requests to the target endpoint
 */
class RequestHandler {
  constructor(baseUrl, timeout = 30000) {
    this.baseUrl = baseUrl;
    this.timeout = timeout;

    // Determine protocol and create appropriate agent
    const url = new URL(baseUrl);
    this.isHttps = url.protocol === "https:";

    if (this.isHttps) {
      this.agent = new https.Agent({
        keepAlive: true,
        maxSockets: 200,
      });
      this.httpModule = https;
    } else {
      this.agent = new http.Agent({
        keepAlive: true,
        maxSockets: 200,
      });
      this.httpModule = http;
    }

    logger.info("RequestHandler initialized", {
      baseUrl: this.baseUrl,
      protocol: url.protocol,
      isHttps: this.isHttps,
    });
  }

  /**
   * Make HTTP/HTTPS request to specified endpoint path
   * @param {string} path - Endpoint path to request
   * @returns {Promise} Promise resolving to response data
   */
  makeRequest(path = "/") {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const url = new URL(path, this.baseUrl);

      logger.debug("Making request", {
        url: url.toString(),
        protocol: url.protocol,
        isHttps: this.isHttps,
      });

      const requestOptions = {
        agent: this.agent,
        timeout: this.timeout,
        headers: {
          "User-Agent": "AutoscalingTest/1.0",
          Connection: "keep-alive",
        },
      };

      const request = this.httpModule.get(url, requestOptions, (response) => {
        let data = "";

        response.on("data", (chunk) => {
          data += chunk;
        });

        response.on("end", () => {
          const responseTime = Date.now() - startTime;

          resolve({
            statusCode: response.statusCode,
            responseTime,
            data: data,
            headers: response.headers,
          });
        });
      });

      request.on("error", (error) => {
        logger.error("Request error", {
          error: error.message,
          url: url.toString(),
          protocol: url.protocol,
        });
        reject(error);
      });

      request.on("timeout", () => {
        const timeoutError = new Error("Request timeout");
        timeoutError.code = "TIMEOUT";
        logger.warn("Request timeout", {
          url: url.toString(),
          timeout: this.timeout,
        });
        request.destroy();
        reject(timeoutError);
      });
    });
  }
}

/**
 * Load generator for simulating concurrent user traffic
 */
class LoadGenerator {
  constructor(requestHandler, metrics) {
    this.requestHandler = requestHandler;
    this.metrics = metrics;
    this.activeRequests = new Set();
    this.isRunning = false;
  }

  /**
   * Start load generation with specified concurrency
   * @param {number} concurrency - Number of concurrent requests to maintain
   * @param {number} interval - Base interval between request batches
   * @param {Array} endpointPaths - Array of endpoint paths to test
   */
  async startLoad(concurrency, interval, endpointPaths = ["/"]) {
    this.isRunning = true;
    logger.info("Starting load generation", { concurrency, interval });

    const requestPromises = [];

    // Generate concurrent requests
    for (let i = 0; i < concurrency; i++) {
      const promise = this.generateContinuousRequests(
        interval,
        endpointPaths,
        i
      );
      requestPromises.push(promise);
    }

    // Wait for all request generators to complete
    await Promise.all(requestPromises);
    logger.info("Load generation completed");
  }

  /**
   * Generate continuous requests for a single worker
   * @param {number} baseInterval - Base interval between requests
   * @param {Array} endpointPaths - Array of endpoint paths to test
   * @param {number} workerId - Unique identifier for this worker
   */
  async generateContinuousRequests(baseInterval, endpointPaths, workerId) {
    while (this.isRunning) {
      try {
        // Randomly select an endpoint to test
        const path =
          endpointPaths[Math.floor(Math.random() * endpointPaths.length)];

        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 100;
        const interval = baseInterval + jitter;

        // Make the request and track metrics
        const requestPromise = this.makeTrackedRequest(path, workerId);
        this.activeRequests.add(requestPromise);

        requestPromise.finally(() => {
          this.activeRequests.delete(requestPromise);
        });

        // Wait for the interval before next request
        await this.sleep(interval);
      } catch (error) {
        logger.error("Error in request generator", {
          error: error.message,
          workerId,
        });
        await this.sleep(1000); // Back off on error
      }
    }
  }

  /**
   * Make a request and track its metrics
   * @param {string} path - Endpoint path to request
   * @param {number} workerId - Worker ID for logging
   */
  async makeTrackedRequest(path, workerId) {
    try {
      const response = await this.requestHandler.makeRequest(path);
      this.metrics.recordSuccess(response.responseTime, response.statusCode);

      logger.debug("Request completed successfully", {
        path,
        workerId,
        statusCode: response.statusCode,
        responseTime: response.responseTime,
      });
    } catch (error) {
      this.metrics.recordFailure(error);
      logger.warn("Request failed", {
        path,
        workerId,
        error: error.message,
      });
    }
  }

  /**
   * Stop load generation gracefully
   */
  async stopLoad() {
    logger.info("Stopping load generation");
    this.isRunning = false;

    // Wait for active requests to complete
    if (this.activeRequests.size > 0) {
      logger.info(
        `Waiting for ${this.activeRequests.size} active requests to complete`
      );
      await Promise.allSettled([...this.activeRequests]);
    }

    logger.info("Load generation stopped");
  }

  /**
   * Sleep utility function
   * @param {number} ms - Milliseconds to sleep
   */
  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

/**
 * Main autoscaling test orchestrator
 */
class AutoscalingTest {
  constructor(config) {
    this.config = config;
    this.metrics = new MetricsTracker();
    this.requestHandler = new RequestHandler(
      config.endpoint,
      config.requestTimeout
    );
    this.loadGenerator = new LoadGenerator(this.requestHandler, this.metrics);
    this.metricsTimer = null;
  }

  /**
   * Start the autoscaling test with all configured phases
   */
  async runTest() {
    logger.info("Starting autoscaling test", {
      endpoint: this.config.endpoint,
      phases: this.config.phases.length,
      totalDuration: this.config.phases.reduce(
        (total, phase) => total + phase.duration,
        0
      ),
    });

    // Start metrics collection
    this.startMetricsCollection();

    try {
      // Execute each test phase sequentially
      for (let i = 0; i < this.config.phases.length; i++) {
        const phase = this.config.phases[i];
        await this.executePhase(phase, i + 1);

        // Brief pause between phases for observation
        if (i < this.config.phases.length - 1) {
          logger.info("Phase transition pause");
          await this.sleep(2000);
        }
      }

      logger.info("Autoscaling test completed successfully");
    } catch (error) {
      logger.error("Autoscaling test failed", { error: error.message });
      throw error;
    } finally {
      // Clean up
      await this.cleanup();
    }
  }

  /**
   * Execute a single test phase
   * @param {Object} phase - Phase configuration object
   * @param {number} phaseNumber - Phase number for logging
   */
  async executePhase(phase, phaseNumber) {
    logger.info(`Starting phase ${phaseNumber}: ${phase.name}`, {
      duration: phase.duration,
      concurrency: phase.concurrency,
      rampUp: phase.rampUp,
    });

    // Get endpoint paths to test
    const endpointPaths = Object.values(this.config.endpoints);

    if (phase.rampUp) {
      // Gradual ramp up to target concurrency
      await this.rampUpPhase(phase, endpointPaths);
    } else {
      // Direct load application
      await this.directLoadPhase(phase, endpointPaths);
    }

    logger.info(`Completed phase ${phaseNumber}: ${phase.name}`);
  }

  /**
   * Execute phase with gradual ramp up
   * @param {Object} phase - Phase configuration
   * @param {Array} endpointPaths - Endpoint paths to test
   */
  async rampUpPhase(phase, endpointPaths) {
    const rampUpDuration = Math.min(phase.duration * 0.3, 30000); // 30% of phase or max 30s
    const steadyDuration = phase.duration - rampUpDuration;

    logger.info("Ramping up load", {
      targetConcurrency: phase.concurrency,
      rampUpDuration,
      steadyDuration,
    });

    // Ramp up gradually
    const rampUpSteps = 5;
    const stepDuration = rampUpDuration / rampUpSteps;

    for (let step = 1; step <= rampUpSteps; step++) {
      const currentConcurrency = Math.ceil(
        (step / rampUpSteps) * phase.concurrency
      );

      logger.info(`Ramp up step ${step}/${rampUpSteps}`, {
        concurrency: currentConcurrency,
      });

      // Start load generator for this step
      const loadPromise = this.loadGenerator.startLoad(
        currentConcurrency,
        this.config.requestInterval,
        endpointPaths
      );

      // Run for step duration
      await Promise.race([loadPromise, this.sleep(stepDuration)]);

      // Stop current load before next step
      await this.loadGenerator.stopLoad();
    }

    // Steady state at target concurrency
    logger.info("Entering steady state", { concurrency: phase.concurrency });

    const steadyLoadPromise = this.loadGenerator.startLoad(
      phase.concurrency,
      this.config.requestInterval,
      endpointPaths
    );

    await Promise.race([steadyLoadPromise, this.sleep(steadyDuration)]);

    await this.loadGenerator.stopLoad();
  }

  /**
   * Execute phase with direct load application
   * @param {Object} phase - Phase configuration
   * @param {Array} endpointPaths - Endpoint paths to test
   */
  async directLoadPhase(phase, endpointPaths) {
    logger.info("Applying direct load", { concurrency: phase.concurrency });

    const loadPromise = this.loadGenerator.startLoad(
      phase.concurrency,
      this.config.requestInterval,
      endpointPaths
    );

    await Promise.race([loadPromise, this.sleep(phase.duration)]);

    await this.loadGenerator.stopLoad();
  }

  /**
   * Start periodic metrics collection and reporting
   */
  startMetricsCollection() {
    logger.info("Starting metrics collection", {
      interval: this.config.metricsInterval,
    });

    this.metricsTimer = setInterval(() => {
      const summary = this.metrics.getSummary();

      logger.info("Metrics update", {
        ...summary,
        timestamp: new Date().toISOString(),
      });

      // Log detailed metrics for analysis
      console.log("\n=== LIVE METRICS ===");
      console.log(
        `Requests: ${
          summary.totalRequests
        } (${summary.requestsPerSecond.toFixed(2)}/s)`
      );
      console.log(`Success Rate: ${summary.successRate.toFixed(2)}%`);
      console.log(
        `Response Times: Avg=${summary.avgResponseTime}ms, P95=${summary.p95ResponseTime}ms, P99=${summary.p99ResponseTime}ms`
      );
      console.log(`Status Codes: ${JSON.stringify(summary.statusCodes)}`);
      if (Object.keys(summary.errors).length > 0) {
        console.log(`Errors: ${JSON.stringify(summary.errors)}`);
      }
      console.log("==================\n");
    }, this.config.metricsInterval);
  }

  /**
   * Clean up resources and stop metrics collection
   */
  async cleanup() {
    logger.info("Cleaning up test resources");

    // Stop metrics collection
    if (this.metricsTimer) {
      clearInterval(this.metricsTimer);
      this.metricsTimer = null;
    }

    // Ensure load generator is stopped
    await this.loadGenerator.stopLoad();

    // Final metrics report
    const finalMetrics = this.metrics.getSummary();

    logger.info("Final test results", finalMetrics);

    console.log("\n=== FINAL TEST RESULTS ===");
    console.log(
      `Total Duration: ${(finalMetrics.duration / 1000).toFixed(2)}s`
    );
    console.log(`Total Requests: ${finalMetrics.totalRequests}`);
    console.log(`Success Rate: ${finalMetrics.successRate.toFixed(2)}%`);
    console.log(`Average RPS: ${finalMetrics.requestsPerSecond.toFixed(2)}`);
    console.log(`Response Times:`);
    console.log(`  Average: ${finalMetrics.avgResponseTime}ms`);
    console.log(`  P50: ${finalMetrics.p50ResponseTime}ms`);
    console.log(`  P95: ${finalMetrics.p95ResponseTime}ms`);
    console.log(`  P99: ${finalMetrics.p99ResponseTime}ms`);
    console.log(`  Min: ${finalMetrics.minResponseTime}ms`);
    console.log(`  Max: ${finalMetrics.maxResponseTime}ms`);
    console.log(
      `Status Code Distribution: ${JSON.stringify(
        finalMetrics.statusCodes,
        null,
        2
      )}`
    );

    if (Object.keys(finalMetrics.errors).length > 0) {
      console.log(
        `Error Distribution: ${JSON.stringify(finalMetrics.errors, null, 2)}`
      );
    }
    console.log("========================\n");
  }

  /**
   * Sleep utility function
   * @param {number} ms - Milliseconds to sleep
   */
  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    logger.info("Initializing autoscaling test", { config: testConfig });

    // Create and run the autoscaling test
    const test = new AutoscalingTest(testConfig);
    await test.runTest();

    logger.info("Autoscaling test completed successfully");
    process.exit(0);
  } catch (error) {
    logger.error("Autoscaling test failed", {
      error: error.message,
      stack: error.stack,
    });
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  logger.info("Received SIGINT, shutting down gracefully");
  process.exit(0);
});

process.on("SIGTERM", () => {
  logger.info("Received SIGTERM, shutting down gracefully");
  process.exit(0);
});

// Start the test if this file is run directly
if (require.main === module) {
  main();
}

module.exports = {
  AutoscalingTest,
  MetricsTracker,
  RequestHandler,
  LoadGenerator,
  testConfig,
};
