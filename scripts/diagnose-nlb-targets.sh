#!/bin/bash

# Diagnose Network Load Balancer Target Registration Issues
# Helps identify why targets show 0 Healthy, 0 Unhealthy

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
CLUSTER_NAME="express-fargate-cluster"
SERVICE_NAME="express-fargate-service"
AWS_REGION="ap-southeast-1"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "=== ECS Fargate NLB Target Diagnosis ==="

# 1. Check ECS Service Status
print_status "1. Checking ECS Service Status..."
aws ecs describe-services \
    --cluster $CLUSTER_NAME \
    --services $SERVICE_NAME \
    --region $AWS_REGION \
    --query 'services[0].{Status:status,RunningCount:runningCount,DesiredCount:desiredCount,PendingCount:pendingCount}' \
    --output table

# 2. Check Load Balancer Configuration
print_status "2. Checking Load Balancer Configuration..."
LB_ARN=$(aws elbv2 describe-load-balancers \
    --region $AWS_REGION \
    --query 'LoadBalancers[?contains(LoadBalancerName, `express-fargate-service`) || contains(LoadBalancerName, `EcsFar`)].LoadBalancerArn' \
    --output text)

if [ -n "$LB_ARN" ] && [ "$LB_ARN" != "None" ]; then
    print_success "Found Load Balancer: $LB_ARN"
    
    # Get load balancer details
    aws elbv2 describe-load-balancers \
        --load-balancer-arns $LB_ARN \
        --region $AWS_REGION \
        --query 'LoadBalancers[0].{DNSName:DNSName,State:State.Code,Type:Type}' \
        --output table
else
    print_error "Could not find load balancer"
fi

# 3. Check Target Groups
print_status "3. Checking Target Groups..."
TARGET_GROUP_ARN=$(aws elbv2 describe-target-groups \
    --region $AWS_REGION \
    --query 'TargetGroups[?contains(TargetGroupName, `express-fargate-service`) || contains(TargetGroupName, `EcsFar`)].TargetGroupArn' \
    --output text)

if [ -n "$TARGET_GROUP_ARN" ] && [ "$TARGET_GROUP_ARN" != "None" ]; then
    print_success "Found Target Group: $TARGET_GROUP_ARN"
    
    # Get target group configuration
    print_status "Target Group Configuration:"
    aws elbv2 describe-target-groups \
        --target-group-arns $TARGET_GROUP_ARN \
        --region $AWS_REGION \
        --query 'TargetGroups[0].{Port:Port,Protocol:Protocol,TargetType:TargetType,HealthCheckPath:HealthCheckPath,HealthCheckPort:HealthCheckPort,HealthCheckProtocol:HealthCheckProtocol}' \
        --output table
        
    # Check target health
    print_status "Target Health Status:"
    aws elbv2 describe-target-health \
        --target-group-arn $TARGET_GROUP_ARN \
        --region $AWS_REGION \
        --query 'TargetHealthDescriptions[*].[Target.Id,Target.Port,TargetHealth.State,TargetHealth.Description]' \
        --output table
        
    # Count targets
    TARGET_COUNT=$(aws elbv2 describe-target-health \
        --target-group-arn $TARGET_GROUP_ARN \
        --region $AWS_REGION \
        --query 'length(TargetHealthDescriptions)' \
        --output text)
    
    print_status "Total registered targets: $TARGET_COUNT"
    
    if [ "$TARGET_COUNT" = "0" ]; then
        print_error "NO TARGETS REGISTERED! This is the root cause."
        print_warning "This usually means:"
        print_warning "  1. ECS service is not properly associated with target group"
        print_warning "  2. Port mapping configuration is wrong"
        print_warning "  3. Security group issues"
        print_warning "  4. Subnet configuration problems"
    fi
else
    print_error "Could not find target group"
fi

# 4. Check ECS Tasks and their network configuration
print_status "4. Checking ECS Tasks..."
TASK_ARNS=$(aws ecs list-tasks \
    --cluster $CLUSTER_NAME \
    --service-name $SERVICE_NAME \
    --region $AWS_REGION \
    --query 'taskArns' \
    --output text)

if [ -n "$TASK_ARNS" ] && [ "$TASK_ARNS" != "None" ]; then
    for task in $TASK_ARNS; do
        print_status "Task: $task"
        
        # Get task details
        aws ecs describe-tasks \
            --cluster $CLUSTER_NAME \
            --tasks $task \
            --region $AWS_REGION \
            --query 'tasks[0].{LastStatus:lastStatus,HealthStatus:healthStatus,CreatedAt:createdAt}' \
            --output table
            
        # Get task network details
        print_status "Network configuration for task:"
        aws ecs describe-tasks \
            --cluster $CLUSTER_NAME \
            --tasks $task \
            --region $AWS_REGION \
            --query 'tasks[0].attachments[0].details[?name==`privateIPv4Address` || name==`networkInterfaceId`].[name,value]' \
            --output table
    done
else
    print_error "No tasks found!"
fi

# 5. Check Load Balancer Listeners
print_status "5. Checking Load Balancer Listeners..."
if [ -n "$LB_ARN" ] && [ "$LB_ARN" != "None" ]; then
    aws elbv2 describe-listeners \
        --load-balancer-arn $LB_ARN \
        --region $AWS_REGION \
        --query 'Listeners[*].{Port:Port,Protocol:Protocol,DefaultActions:DefaultActions[0].TargetGroupArn}' \
        --output table
fi

# 6. Security Group Check
print_status "6. Checking Security Groups..."
if [ -n "$TASK_ARNS" ] && [ "$TASK_ARNS" != "None" ]; then
    # Get first task for security group info
    FIRST_TASK=$(echo $TASK_ARNS | cut -d' ' -f1)
    
    # Get ENI ID
    ENI_ID=$(aws ecs describe-tasks \
        --cluster $CLUSTER_NAME \
        --tasks $FIRST_TASK \
        --region $AWS_REGION \
        --query 'tasks[0].attachments[0].details[?name==`networkInterfaceId`].value' \
        --output text)
    
    if [ -n "$ENI_ID" ] && [ "$ENI_ID" != "None" ]; then
        print_status "Checking security groups for ENI: $ENI_ID"
        aws ec2 describe-network-interfaces \
            --network-interface-ids $ENI_ID \
            --region $AWS_REGION \
            --query 'NetworkInterfaces[0].Groups[*].{GroupId:GroupId,GroupName:GroupName}' \
            --output table
    fi
fi

print_status "=== Diagnosis Complete ==="
print_warning "If targets are not registering (0 targets), check:"
print_warning "1. ECS service load balancer configuration"
print_warning "2. Target group port configuration (should be 3000)"
print_warning "3. Container port mapping in task definition"
print_warning "4. Security group rules allowing traffic on port 3000" 