{"name": "autoscaling-test", "version": "1.0.0", "description": "Load testing script to test autoscaling behavior of ECS Fargate Express application", "main": "autoscaling-test.js", "scripts": {"test": "node autoscaling-test.js", "test:quick": "node -e \"const config = require('./autoscaling-test').testConfig; config.phases = [{name: 'quick-test', duration: 30000, concurrency: 10, rampUp: true}]; const {AutoscalingTest} = require('./autoscaling-test'); new AutoscalingTest(config).runTest();\"", "test:stress": "node -e \"const config = require('./autoscaling-test').testConfig; config.phases = [{name: 'stress-test', duration: 600000, concurrency: 200, rampUp: true}]; const {AutoscalingTest} = require('./autoscaling-test'); new AutoscalingTest(config).runTest();\""}, "dependencies": {"winston": "^3.11.0"}, "keywords": ["load-testing", "autoscaling", "aws", "ecs", "fargate", "performance"], "author": "Startup Learn Team", "license": "MIT"}