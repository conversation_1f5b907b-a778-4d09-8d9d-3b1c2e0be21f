#!/bin/bash

# ECS Scheduler Management Script
# Provides utilities to test, monitor, and manage the ECS scheduler

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CLUSTER_NAME="express-fargate-cluster"
SERVICE_NAME="express-fargate-service"
STACK_NAME="EcsFargateAndSpotStack"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if AWS CLI is configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed or not in PATH"
        exit 1
    fi

    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured or credentials are invalid"
        exit 1
    fi

    print_success "AWS CLI is configured and working"
}

# Function to get current ECS service status
get_service_status() {
    print_status "Getting current ECS service status..."

    local service_info=$(aws ecs describe-services \
        --cluster "$CLUSTER_NAME" \
        --services "$SERVICE_NAME" \
        --query 'services[0].{DesiredCount:desiredCount,RunningCount:runningCount,PendingCount:pendingCount,Status:status}' \
        --output table 2>/dev/null)

    if [ $? -eq 0 ]; then
        echo "$service_info"

        # Get capacity provider strategy
        print_status "Current capacity provider strategy:"
        aws ecs describe-services \
            --cluster "$CLUSTER_NAME" \
            --services "$SERVICE_NAME" \
            --query 'services[0].capacityProviderStrategy[*].{Provider:capacityProvider,Weight:weight,Base:base}' \
            --output table 2>/dev/null || print_warning "Could not retrieve capacity provider strategy"
    else
        print_error "Could not retrieve service status. Check if service exists."
    fi
}

# Function to get auto-scaling status
get_autoscaling_status() {
    print_status "Getting auto-scaling configuration..."

    aws application-autoscaling describe-scalable-targets \
        --service-namespace ecs \
        --resource-ids "service/$CLUSTER_NAME/$SERVICE_NAME" \
        --query 'ScalableTargets[0].{MinCapacity:MinCapacity,MaxCapacity:MaxCapacity,RoleARN:RoleARN}' \
        --output table 2>/dev/null || print_warning "Could not retrieve auto-scaling configuration"
}

# Function to test business hours scheduler
test_business_hours() {
    print_status "Testing business hours scheduler..."

    # Find the actual function name (CDK adds suffixes)
    local function_name=$(aws lambda list-functions --query 'Functions[?contains(FunctionName, `BusinessHoursScheduler`)].FunctionName' --output text)

    if [ -z "$function_name" ]; then
        print_error "Business hours scheduler function not found"
        print_status "Available scheduler functions:"
        aws lambda list-functions --query 'Functions[?contains(FunctionName, `Scheduler`)].FunctionName' --output table
        return 1
    fi

    print_status "Found function: $function_name"

    # Invoke function
    print_status "Invoking business hours scheduler..."
    aws lambda invoke \
        --function-name "$function_name" \
        --payload '{}' \
        /tmp/business-hours-response.json

    if [ $? -eq 0 ]; then
        print_success "Business hours scheduler invoked successfully"
        echo "Response:"
        cat /tmp/business-hours-response.json | jq . 2>/dev/null || cat /tmp/business-hours-response.json
        rm -f /tmp/business-hours-response.json

        print_status "Waiting 30 seconds for ECS service to update..."
        sleep 30
        get_service_status
    else
        print_error "Failed to invoke business hours scheduler"
        return 1
    fi
}

# Function to test off-hours scheduler
test_off_hours() {
    print_status "Testing off-hours scheduler..."

    # Find the actual function name (CDK adds suffixes)
    local function_name=$(aws lambda list-functions --query 'Functions[?contains(FunctionName, `OffHoursScheduler`)].FunctionName' --output text)

    if [ -z "$function_name" ]; then
        print_error "Off-hours scheduler function not found"
        print_status "Available scheduler functions:"
        aws lambda list-functions --query 'Functions[?contains(FunctionName, `Scheduler`)].FunctionName' --output table
        return 1
    fi

    print_status "Found function: $function_name"

    # Invoke function
    print_status "Invoking off-hours scheduler..."
    aws lambda invoke \
        --function-name "$function_name" \
        --payload '{}' \
        /tmp/off-hours-response.json

    if [ $? -eq 0 ]; then
        print_success "Off-hours scheduler invoked successfully"
        echo "Response:"
        cat /tmp/off-hours-response.json | jq . 2>/dev/null || cat /tmp/off-hours-response.json
        rm -f /tmp/off-hours-response.json

        print_status "Waiting 30 seconds for ECS service to update..."
        sleep 30
        get_service_status
    else
        print_error "Failed to invoke off-hours scheduler"
        return 1
    fi
}

# Function to check EventBridge rules
check_eventbridge_rules() {
    print_status "Checking EventBridge rules..."

    # List rules related to our stack
    local rules=$(aws events list-rules \
        --query "Rules[?contains(Name, '$STACK_NAME')].{Name:Name,Schedule:ScheduleExpression,State:State}" \
        --output table 2>/dev/null)

    if [ $? -eq 0 ] && [ -n "$rules" ]; then
        echo "$rules"
    else
        print_warning "No EventBridge rules found for stack: $STACK_NAME"
    fi
}

# Function to monitor scheduler logs
monitor_logs() {
    local scheduler_type="$1"

    if [ "$scheduler_type" = "business" ]; then
        local function_name=$(aws lambda list-functions --query 'Functions[?contains(FunctionName, `BusinessHoursScheduler`)].FunctionName' --output text)
        print_status "Monitoring business hours scheduler logs..."
    elif [ "$scheduler_type" = "off" ]; then
        local function_name=$(aws lambda list-functions --query 'Functions[?contains(FunctionName, `OffHoursScheduler`)].FunctionName' --output text)
        print_status "Monitoring off-hours scheduler logs..."
    else
        print_error "Invalid scheduler type. Use 'business' or 'off'"
        return 1
    fi

    if [ -z "$function_name" ]; then
        print_error "Scheduler function not found for type: $scheduler_type"
        return 1
    fi

    local log_group="/aws/lambda/$function_name"

    print_status "Tailing logs for: $log_group"
    print_status "Press Ctrl+C to stop monitoring"

    aws logs tail "$log_group" --follow 2>/dev/null || {
        print_error "Could not access logs for $log_group"
        print_warning "Make sure the function has been invoked at least once"
    }
}

# Function to show help
show_help() {
    echo "ECS Scheduler Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  status              Show current ECS service and auto-scaling status"
    echo "  test-business       Test business hours scheduler (scales to 2 tasks)"
    echo "  test-off            Test off-hours scheduler (scales to 1 task)"
    echo "  check-rules         Check EventBridge rules status"
    echo "  monitor-business    Monitor business hours scheduler logs"
    echo "  monitor-off         Monitor off-hours scheduler logs"
    echo "  help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 status                    # Check current service status"
    echo "  $0 test-business            # Test business hours configuration"
    echo "  $0 test-off                 # Test off-hours configuration"
    echo "  $0 monitor-business         # Monitor business hours logs"
    echo ""
}

# Main script logic
main() {
    local command="$1"

    # Check AWS CLI first
    check_aws_cli

    case "$command" in
        "status")
            get_service_status
            echo ""
            get_autoscaling_status
            ;;
        "test-business")
            test_business_hours
            ;;
        "test-off")
            test_off_hours
            ;;
        "check-rules")
            check_eventbridge_rules
            ;;
        "monitor-business")
            monitor_logs "business"
            ;;
        "monitor-off")
            monitor_logs "off"
            ;;
        "help"|"--help"|"-h"|"")
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
