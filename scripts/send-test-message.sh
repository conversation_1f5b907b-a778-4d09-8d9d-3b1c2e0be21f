#!/bin/bash

# Script to send test messages to the SQS queue
# Usage: ./send-test-message.sh [message-text] [queue-url]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Get parameters
MESSAGE_BODY="${1:-Hello from SQS test script! Timestamp: $(date)}"
QUEUE_URL="${2}"

# If queue URL is not provided, try to get it from CDK outputs
if [ -z "$QUEUE_URL" ]; then
    print_status "Queue URL not provided, attempting to get from CDK stack outputs..."
    
    # Try to get queue URL from CDK stack exports
    QUEUE_URL=$(aws cloudformation describe-stacks \
        --stack-name SqsLambdaStack \
        --query 'Stacks[0].Outputs[?OutputKey==`QueueUrl`].OutputValue' \
        --output text 2>/dev/null || echo "")
    
    if [ -z "$QUEUE_URL" ] || [ "$QUEUE_URL" = "None" ]; then
        print_error "Could not find queue URL. Please provide it as second parameter."
        echo "Usage: $0 [message-text] [queue-url]"
        echo "Example: $0 'Test message' 'https://sqs.ap-southeast-1.amazonaws.com/123456789012/sqs-lambda-processing-queue'"
        exit 1
    fi
fi

print_status "Using queue URL: $QUEUE_URL"

# Check if the message is a JSON file
if [ -f "$MESSAGE_BODY" ]; then
    print_status "Reading message from file: $MESSAGE_BODY"
    MESSAGE_CONTENT=$(cat "$MESSAGE_BODY")
else
    MESSAGE_CONTENT="$MESSAGE_BODY"
fi

# Send the message
print_status "Sending message to SQS queue..."
print_status "Message content: $MESSAGE_CONTENT"

RESULT=$(aws sqs send-message \
    --queue-url "$QUEUE_URL" \
    --message-body "$MESSAGE_CONTENT" \
    --output json)

if [ $? -eq 0 ]; then
    MESSAGE_ID=$(echo "$RESULT" | jq -r '.MessageId')
    print_status "Message sent successfully!"
    print_status "Message ID: $MESSAGE_ID"
    print_status "You can check CloudWatch Logs to see the Lambda function execution."
    
    # Provide helpful commands
    echo ""
    print_warning "Helpful commands:"
    echo "  View Lambda logs: aws logs tail /aws/lambda/sqs-message-processor --follow"
    echo "  Check queue attributes: aws sqs get-queue-attributes --queue-url $QUEUE_URL --attribute-names All"
    echo "  Purge queue (remove all messages): aws sqs purge-queue --queue-url $QUEUE_URL"
else
    print_error "Failed to send message to SQS queue"
    exit 1
fi 