#!/bin/bash

# Script to monitor Lambda processing and SQS queue status in real-time
# Usage: ./monitor-processing.sh [queue-url]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_metric() {
    echo -e "${BLUE}[METRIC]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get queue URL
QUEUE_URL="${1}"

# If queue URL is not provided, try to get it from CDK outputs
if [ -z "$QUEUE_URL" ]; then
    print_status "Queue URL not provided, attempting to get from CDK stack outputs..."
    
    QUEUE_URL=$(aws cloudformation describe-stacks \
        --stack-name SqsLambdaStack \
        --query 'Stacks[0].Outputs[?OutputKey==`QueueUrl`].OutputValue' \
        --output text 2>/dev/null || echo "")
    
    if [ -z "$QUEUE_URL" ] || [ "$QUEUE_URL" = "None" ]; then
        echo "Could not find queue URL. Please provide it as parameter."
        echo "Usage: $0 [queue-url]"
        exit 1
    fi
fi

# Get DLQ URL
DLQ_URL=$(aws cloudformation describe-stacks \
    --stack-name SqsLambdaStack \
    --query 'Stacks[0].Outputs[?OutputKey==`DeadLetterQueueUrl`].OutputValue' \
    --output text 2>/dev/null || echo "")

print_status "Monitoring SQS Queue: $QUEUE_URL"
if [ ! -z "$DLQ_URL" ]; then
    print_status "Dead Letter Queue: $DLQ_URL"
fi

# Function to get queue attributes
get_queue_stats() {
    local queue_url=$1
    local queue_name=$2
    
    local stats=$(aws sqs get-queue-attributes \
        --queue-url "$queue_url" \
        --attribute-names ApproximateNumberOfMessages,ApproximateNumberOfMessagesNotVisible \
        --output json 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        local visible=$(echo "$stats" | jq -r '.Attributes.ApproximateNumberOfMessages // "0"')
        local in_flight=$(echo "$stats" | jq -r '.Attributes.ApproximateNumberOfMessagesNotVisible // "0"')
        echo "$queue_name: $visible messages waiting, $in_flight in-flight"
    else
        echo "$queue_name: Unable to get stats"
    fi
}

# Function to get Lambda metrics
get_lambda_metrics() {
    # Get recent invocations (last 5 minutes)
    local end_time=$(date -u +"%Y-%m-%dT%H:%M:%S")
    local start_time=$(date -u -v-5M +"%Y-%m-%dT%H:%M:%S" 2>/dev/null || date -u -d '5 minutes ago' +"%Y-%m-%dT%H:%M:%S")
    
    # Count recent log entries
    local recent_invocations=$(aws logs filter-log-events \
        --log-group-name "/aws/lambda/sqs-message-processor" \
        --start-time $(date -d "$start_time" +%s)000 2>/dev/null \
        --filter-pattern "Lambda function started" \
        --query 'length(events)' --output text 2>/dev/null || echo "0")
    
    echo "Lambda invocations (last 5 min): $recent_invocations"
    
    # Show current concurrent executions if available
    local current_time=$(date +%s)
    local five_min_ago=$((current_time - 300))
    
    # Get CloudWatch metrics for concurrent executions
    local concurrent_exec=$(aws cloudwatch get-metric-statistics \
        --namespace AWS/Lambda \
        --metric-name ConcurrentExecutions \
        --dimensions Name=FunctionName,Value=sqs-message-processor \
        --start-time $(date -u -d "@$five_min_ago" +"%Y-%m-%dT%H:%M:%S") \
        --end-time $(date -u -d "@$current_time" +"%Y-%m-%dT%H:%M:%S") \
        --period 60 \
        --statistics Maximum \
        --query 'Datapoints[-1].Maximum' \
        --output text 2>/dev/null || echo "N/A")
    
    if [ "$concurrent_exec" != "None" ] && [ "$concurrent_exec" != "N/A" ]; then
        echo "Current concurrent executions: $concurrent_exec"
    fi
    
    # Check for throttles
    local throttles=$(aws cloudwatch get-metric-statistics \
        --namespace AWS/Lambda \
        --metric-name Throttles \
        --dimensions Name=FunctionName,Value=sqs-message-processor \
        --start-time $(date -u -d "@$five_min_ago" +"%Y-%m-%dT%H:%M:%S") \
        --end-time $(date -u -d "@$current_time" +"%Y-%m-%dT%H:%M:%S") \
        --period 300 \
        --statistics Sum \
        --query 'Datapoints[-1].Sum' \
        --output text 2>/dev/null || echo "0")
    
    if [ "$throttles" != "None" ] && [ "$throttles" != "0" ]; then
        print_warning "Lambda throttles detected: $throttles"
    fi
}

# Function to show recent log entries
show_recent_logs() {
    echo ""
    echo "Recent Lambda log entries (last 2 minutes):"
    echo "----------------------------------------"
    
    aws logs filter-log-events \
        --log-group-name "/aws/lambda/sqs-message-processor" \
        --start-time $(($(date +%s) - 120))000 \
        --query 'events[].message' \
        --output text 2>/dev/null | head -10 || echo "No recent logs found"
}

print_header "SQS Lambda Concurrency Monitor"
print_status "Press Ctrl+C to stop monitoring"
print_status "No concurrency limits set - watch natural Lambda scaling behavior"

echo ""

# Main monitoring loop
while true; do
    clear
    print_header "SQS Lambda Concurrency Monitor - $(date)"
    
    echo ""
    print_metric "Queue Status:"
    get_queue_stats "$QUEUE_URL" "Main Queue"
    
    if [ ! -z "$DLQ_URL" ]; then
        get_queue_stats "$DLQ_URL" "Dead Letter Queue"
    fi
    
    echo ""
    print_metric "Lambda Status:"
    get_lambda_metrics
    
    # Show recent processing
    show_recent_logs
    
    echo ""
    print_status "Refreshing in 5 seconds... (Ctrl+C to stop)"
    echo ""
    print_warning "Tip: In another terminal, run:"
    echo "  ./send-multiple-messages.sh 100    # Send 100 messages"
    echo "  aws logs tail /aws/lambda/sqs-message-processor --follow"
    
    sleep 5
done 