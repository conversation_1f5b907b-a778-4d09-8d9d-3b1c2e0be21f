#!/bin/bash

# Force Stable ECS Deployment Script
# Helps resolve stuck deployments with unhealthy tasks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
CLUSTER_NAME="express-fargate-cluster"
SERVICE_NAME="express-fargate-service"
AWS_REGION="ap-southeast-1"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Starting ECS deployment stabilization process..."

# Function to get service status
get_service_status() {
    aws ecs describe-services \
        --cluster $CLUSTER_NAME \
        --services $SERVICE_NAME \
        --region $AWS_REGION \
        --query 'services[0].deployments[0].status' \
        --output text
}

# Function to get running task count
get_running_tasks() {
    aws ecs describe-services \
        --cluster $CLUSTER_NAME \
        --services $SERVICE_NAME \
        --region $AWS_REGION \
        --query 'services[0].runningCount' \
        --output text
}

# Function to get desired count
get_desired_count() {
    aws ecs describe-services \
        --cluster $CLUSTER_NAME \
        --services $SERVICE_NAME \
        --region $AWS_REGION \
        --query 'services[0].desiredCount' \
        --output text
}

# Function to list unhealthy tasks
list_unhealthy_tasks() {
    aws ecs list-tasks \
        --cluster $CLUSTER_NAME \
        --service-name $SERVICE_NAME \
        --desired-status RUNNING \
        --region $AWS_REGION \
        --query 'taskArns' \
        --output text
}

# Function to stop all tasks (force restart)
force_restart_service() {
    print_warning "Forcing service restart by stopping all tasks..."
    
    # Get all running tasks
    TASK_ARNS=$(aws ecs list-tasks \
        --cluster $CLUSTER_NAME \
        --service-name $SERVICE_NAME \
        --region $AWS_REGION \
        --query 'taskArns' \
        --output text)
    
    if [ -n "$TASK_ARNS" ] && [ "$TASK_ARNS" != "None" ]; then
        for task in $TASK_ARNS; do
            print_status "Stopping task: $task"
            aws ecs stop-task \
                --cluster $CLUSTER_NAME \
                --task $task \
                --reason "Force restart for stable deployment" \
                --region $AWS_REGION > /dev/null
        done
        print_success "All tasks stopped. New tasks will be created automatically."
    else
        print_warning "No running tasks found to stop."
    fi
}

# Function to wait for stable deployment
wait_for_stable_deployment() {
    print_status "Waiting for deployment to stabilize..."
    local max_attempts=20
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        local status=$(get_service_status)
        local running=$(get_running_tasks)
        local desired=$(get_desired_count)
        
        print_status "Attempt $attempt/$max_attempts - Status: $status, Running: $running/$desired"
        
        if [ "$status" = "STEADY" ] && [ "$running" = "$desired" ]; then
            print_success "Deployment is now stable!"
            return 0
        fi
        
        if [ "$status" = "FAILED" ]; then
            print_error "Deployment failed!"
            return 1
        fi
        
        sleep 30
        ((attempt++))
    done
    
    print_error "Deployment did not stabilize within expected time"
    return 1
}

# Function to check target group health
check_target_health() {
    print_status "Checking load balancer target health..."
    
    # Get target group ARN
    TARGET_GROUP_ARN=$(aws elbv2 describe-target-groups \
        --region $AWS_REGION \
        --query 'TargetGroups[?contains(TargetGroupName, `express-fargate-service`)].TargetGroupArn' \
        --output text)
    
    if [ -n "$TARGET_GROUP_ARN" ] && [ "$TARGET_GROUP_ARN" != "None" ]; then
        aws elbv2 describe-target-health \
            --target-group-arn $TARGET_GROUP_ARN \
            --region $AWS_REGION \
            --query 'TargetHealthDescriptions[*].[Target.Id,TargetHealth.State,TargetHealth.Description]' \
            --output table
    else
        print_warning "Could not find target group"
    fi
}

# Function to show recent service events
show_service_events() {
    print_status "Recent service events:"
    aws ecs describe-services \
        --cluster $CLUSTER_NAME \
        --services $SERVICE_NAME \
        --region $AWS_REGION \
        --query 'services[0].events[0:5].[createdAt,message]' \
        --output table
}

# Main execution
print_status "Current service status:"
show_service_events

# Check current deployment status
current_status=$(get_service_status)
print_status "Current deployment status: $current_status"

if [ "$current_status" != "STEADY" ]; then
    print_warning "Deployment is not stable. Checking if force restart is needed..."
    
    # Check if deployment has been stuck for a while
    print_status "Checking target health first..."
    check_target_health
    
    # Ask user if they want to force restart
    echo ""
    read -p "Do you want to force restart the service? (y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        force_restart_service
        wait_for_stable_deployment
    else
        print_status "Monitoring deployment without intervention..."
        wait_for_stable_deployment
    fi
else
    print_success "Service is already stable!"
    check_target_health
fi

# Final status check
print_status "Final deployment status:"
show_service_events
check_target_health

print_success "Deployment stabilization process completed!" 