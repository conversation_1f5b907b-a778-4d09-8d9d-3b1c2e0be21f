#!/bin/bash

# Script to send multiple messages to SQS concurrently
# Usage: ./send-multiple-messages.sh [number-of-messages] [queue-url] [concurrent-batches]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_progress() {
    echo -e "${BLUE}[PROGRESS]${NC} $1"
}

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Get parameters
NUM_MESSAGES="${1:-50}"  # Default 50 messages
QUEUE_URL="${2}"
CONCURRENT_BATCHES="${3:-5}"  # Default 5 concurrent batches

# If queue URL is not provided, try to get it from CDK outputs
if [ -z "$QUEUE_URL" ]; then
    print_status "Queue URL not provided, attempting to get from CDK stack outputs..."
    
    # Try to get queue URL from CDK stack exports
    QUEUE_URL=$(aws cloudformation describe-stacks \
        --stack-name SqsLambdaStack \
        --query 'Stacks[0].Outputs[?OutputKey==`QueueUrl`].OutputValue' \
        --output text 2>/dev/null || echo "")
    
    if [ -z "$QUEUE_URL" ] || [ "$QUEUE_URL" = "None" ]; then
        print_error "Could not find queue URL. Please provide it as second parameter."
        echo "Usage: $0 [number-of-messages] [queue-url] [concurrent-batches]"
        echo "Example: $0 100 'https://sqs.ap-southeast-1.amazonaws.com/123456789012/sqs-lambda-processing-queue' 10"
        exit 1
    fi
fi

print_status "Configuration:"
print_status "  Queue URL: $QUEUE_URL"
print_status "  Number of messages: $NUM_MESSAGES"
print_status "  Concurrent batches: $CONCURRENT_BATCHES"

# Function to send a single message
send_message() {
    local message_id=$1
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")
    
    # Create different types of messages for variety
    local message_types=("process_order" "send_notification" "update_inventory" "generic_task")
    local message_type=${message_types[$((message_id % 4))]}
    
    local message_body=""
    case $message_type in
        "process_order")
            message_body=$(cat <<EOF
{
  "action": "process_order",
  "messageId": $message_id,
  "orderId": "ORD-$message_id",
  "customerId": "CUST-$(($message_id * 2))",
  "items": [
    {"productId": "PROD-001", "quantity": $((message_id % 5 + 1)), "price": 29.99}
  ],
  "totalAmount": $((message_id % 100 + 50)),
  "timestamp": "$timestamp",
  "priority": "$(if [ $((message_id % 3)) -eq 0 ]; then echo "high"; else echo "normal"; fi)",
  "processingTime": $((message_id % 10 + 1))
}
EOF
)
            ;;
        "send_notification")
            message_body=$(cat <<EOF
{
  "action": "send_notification",
  "messageId": $message_id,
  "type": "email",
  "recipient": "user$<EMAIL>",
  "subject": "Test Notification #$message_id",
  "message": "This is test notification message number $message_id sent at $timestamp",
  "priority": "normal",
  "timestamp": "$timestamp"
}
EOF
)
            ;;
        "update_inventory")
            message_body=$(cat <<EOF
{
  "action": "update_inventory",
  "messageId": $message_id,
  "productId": "PROD-$(printf "%03d" $((message_id % 100)))",
  "quantityChange": $((-1 * (message_id % 5 + 1))),
  "currentStock": $((message_id % 1000 + 100)),
  "location": "warehouse-$(if [ $((message_id % 2)) -eq 0 ]; then echo "east"; else echo "west"; fi)",
  "reason": "sale",
  "timestamp": "$timestamp"
}
EOF
)
            ;;
        "generic_task")
            message_body=$(cat <<EOF
{
  "messageId": $message_id,
  "taskType": "data_processing",
  "data": {
    "userId": "user$message_id",
    "sessionId": "sess_$(openssl rand -hex 8)",
    "activity": "batch_processing",
    "batchSize": $((message_id % 20 + 10)),
    "timestamp": "$timestamp"
  },
  "metadata": {
    "source": "load_test_script",
    "version": "1.0"
  }
}
EOF
)
            ;;
    esac
    
    # Send the message and capture result
    local result=$(aws sqs send-message \
        --queue-url "$QUEUE_URL" \
        --message-body "$message_body" \
        --output json 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        local msg_id=$(echo "$result" | jq -r '.MessageId')
        echo "✓ Message $message_id sent successfully (SQS MessageId: ${msg_id:0:8}...)"
    else
        echo "✗ Failed to send message $message_id"
        return 1
    fi
}

# Function to send a batch of messages concurrently
send_batch() {
    local batch_start=$1
    local batch_size=$2
    local batch_end=$((batch_start + batch_size - 1))
    
    print_progress "Sending batch: messages $batch_start to $batch_end"
    
    # Send messages in parallel within this batch
    for ((i=batch_start; i<=batch_end; i++)); do
        send_message $i &
    done
    
    # Wait for all messages in this batch to complete
    wait
    print_progress "Batch $batch_start-$batch_end completed"
}

# Calculate batch size
messages_per_batch=$((NUM_MESSAGES / CONCURRENT_BATCHES))
remaining_messages=$((NUM_MESSAGES % CONCURRENT_BATCHES))

print_status "Starting to send $NUM_MESSAGES messages in $CONCURRENT_BATCHES concurrent batches..."
print_status "Each batch will have approximately $messages_per_batch messages"

# Record start time
start_time=$(date +%s)

# Send messages in concurrent batches
current_message=1
for ((batch=1; batch<=CONCURRENT_BATCHES; batch++)); do
    batch_size=$messages_per_batch
    
    # Add remaining messages to the last batch
    if [ $batch -eq $CONCURRENT_BATCHES ]; then
        batch_size=$((batch_size + remaining_messages))
    fi
    
    # Send batch in background
    send_batch $current_message $batch_size &
    
    current_message=$((current_message + batch_size))
    
    # Small delay between starting batches to avoid overwhelming
    sleep 0.5
done

# Wait for all batches to complete
print_status "Waiting for all batches to complete..."
wait

# Record end time and calculate duration
end_time=$(date +%s)
duration=$((end_time - start_time))

print_status "All $NUM_MESSAGES messages sent successfully!"
print_status "Total time: ${duration} seconds"
print_status "Rate: $((NUM_MESSAGES / duration)) messages/second"

# Provide helpful next steps
echo ""
print_warning "Next steps:"
echo "  1. Check Lambda function logs: aws logs tail /aws/lambda/sqs-message-processor --follow"
echo "  2. Monitor SQS queue: aws sqs get-queue-attributes --queue-url $QUEUE_URL --attribute-names ApproximateNumberOfMessages"
echo "  3. Check Lambda metrics in CloudWatch for concurrency and throttling"
echo "  4. View Lambda function invocations: aws logs filter-log-events --log-group-name /aws/lambda/sqs-message-processor --filter-pattern 'Lambda function started'"

print_status "Monitoring tip: Run this in another terminal to watch real-time processing:"
echo "  watch -n 2 'aws sqs get-queue-attributes --queue-url $QUEUE_URL --attribute-names ApproximateNumberOfMessages,ApproximateNumberOfMessagesNotVisible --output table'" 