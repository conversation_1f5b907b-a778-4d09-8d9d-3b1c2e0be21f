# Autoscaling Test Script

A comprehensive JavaScript load testing script designed to test the autoscaling behavior of your AWS ECS Fargate Express application deployed behind an API Gateway.

## Overview

This script simulates realistic user traffic patterns with gradually increasing load to trigger autoscaling events in your ECS Fargate service. It monitors performance metrics, response times, and success rates to help you validate your autoscaling configuration.

## Features

- **Phased Load Testing**: Gradual ramp-up through multiple test phases
- **Comprehensive Metrics**: Real-time monitoring of response times, success rates, and throughput
- **Winston Logging**: Structured logging with both console and file output
- **Multiple Endpoints**: Tests various application endpoints (/, /health, /health/deep, /status)
- **Graceful Handling**: Proper error handling and graceful shutdown
- **Percentile Analysis**: P50, P95, P99 response time analysis
- **Concurrent Load**: Configurable concurrency levels to simulate real user traffic

## Test Phases

The script runs through the following phases by default:

1. **Warmup** (30s): Single request to initialize connections
2. **Light Load** (1m): 5 concurrent users with gradual ramp-up
3. **Medium Load** (2m): 20 concurrent users with gradual ramp-up
4. **Heavy Load** (3m): 50 concurrent users with gradual ramp-up
5. **Peak Load** (5m): 100 concurrent users with gradual ramp-up
6. **Cool Down** (1m): Return to 5 concurrent users

## Installation

1. Navigate to the scripts directory:

```bash
cd scripts
```

2. Install dependencies:

```bash
npm install
```

## Usage

### Basic Test

Run the full autoscaling test:

```bash
npm run test
```

### Quick Test

Run a shortened test for quick validation:

```bash
npm run test:quick
```

### Stress Test

Run an extended high-load test:

```bash
npm run test:stress
```

### Direct Execution

Run the script directly:

```bash
node autoscaling-test.js
```

## Configuration

The test configuration can be customized by modifying the `testConfig` object in `autoscaling-test.js`:

```javascript
const testConfig = {
  // Target endpoint for testing
  endpoint: "https://00704atez9.execute-api.ap-southeast-1.amazonaws.com/",

  // Test phases configuration
  phases: [
    { name: "warmup", duration: 30000, concurrency: 1, rampUp: false },
    { name: "light-load", duration: 60000, concurrency: 5, rampUp: true },
    // ... more phases
  ],

  // Request configuration
  requestTimeout: 30000,
  requestInterval: 100,

  // Metrics collection interval
  metricsInterval: 5000,

  // Health check endpoints to test
  endpoints: {
    root: "/",
    health: "/health",
    deepHealth: "/health/deep",
    status: "/status",
  },
};
```

### Configuration Options

- **endpoint**: Base URL of your application
- **phases**: Array of test phases with duration, concurrency, and ramp-up settings
- **requestTimeout**: Maximum time to wait for a response (ms)
- **requestInterval**: Base interval between requests (ms)
- **metricsInterval**: How often to log metrics updates (ms)
- **endpoints**: Object defining which endpoints to test

## Output

The script provides real-time metrics every 5 seconds and a comprehensive final report:

```
=== LIVE METRICS ===
Requests: 1250 (25.67/s)
Success Rate: 99.84%
Response Times: Avg=156ms, P95=324ms, P99=567ms
Status Codes: {"200":1248,"500":2}
==================

=== FINAL TEST RESULTS ===
Total Duration: 750.45s
Total Requests: 18,645
Success Rate: 99.73%
Average RPS: 24.85
Response Times:
  Average: 178ms
  P50: 145ms
  P95: 378ms
  P99: 645ms
  Min: 89ms
  Max: 2,145ms
Status Code Distribution: {
  "200": 18580,
  "500": 65
}
========================
```

## Logs

Logs are written to:

- **Console**: Colored, human-readable format
- **File**: `../logs/autoscaling-test.log` in structured JSON format

## Monitoring Autoscaling

During the test, monitor your AWS console to observe:

1. **ECS Service Metrics**: Watch task count increase as load grows
2. **Application Load Balancer**: Monitor target health and request distribution
3. **CloudWatch Metrics**: CPU utilization, memory usage, and custom metrics
4. **API Gateway**: Request count and latency metrics

## Expected Behavior

### Phase 1-2 (Warmup/Light Load)

- Single task should handle the light load
- Response times should be consistent
- No scaling events expected

### Phase 3-4 (Medium/Heavy Load)

- ECS autoscaling should trigger
- Task count should increase (typically 2-4 tasks)
- Response times may spike briefly during scaling

### Phase 5 (Peak Load)

- Maximum task count reached based on your autoscaling configuration
- Response times should stabilize
- Success rate should remain high (>95%)

### Phase 6 (Cool Down)

- Tasks should scale down gradually
- Response times should improve

## Troubleshooting

### High Error Rates

- Check ECS service health
- Verify load balancer target group health
- Review application logs for errors
- Consider increasing task limits

### Slow Response Times

- Monitor CPU/memory utilization
- Check if scaling is occurring fast enough
- Verify network performance
- Review application performance bottlenecks

### No Scaling Events

- Verify autoscaling configuration
- Check CloudWatch alarms
- Ensure scaling policies are properly configured
- Review target tracking metrics

## Customization

You can create custom test scenarios by modifying the script:

```javascript
// Custom quick test
const quickTestConfig = {
  ...testConfig,
  phases: [
    { name: "quick-ramp", duration: 60000, concurrency: 20, rampUp: true },
  ],
};

// Custom stress test
const stressTestConfig = {
  ...testConfig,
  phases: [
    { name: "stress", duration: 600000, concurrency: 200, rampUp: true },
  ],
};
```

## Best Practices

1. **Start Small**: Begin with lower concurrency to establish baselines
2. **Monitor Costs**: High load tests can incur AWS charges
3. **Coordinate with Team**: Notify team members before running large tests
4. **Use Staging**: Test autoscaling in a staging environment first
5. **Analyze Results**: Review logs and metrics to optimize autoscaling configuration

## Integration with CI/CD

You can integrate this script into your CI/CD pipeline for automated autoscaling validation:

```yaml
# Example GitHub Actions step
- name: Run Autoscaling Test
  run: |
    cd scripts
    npm install
    npm run test:quick
```

## Performance Metrics

The script tracks comprehensive metrics including:

- **Throughput**: Requests per second
- **Latency**: Response time percentiles
- **Reliability**: Success rate and error distribution
- **Concurrency**: Active request tracking
- **Resource Usage**: Connection pooling and reuse

## Support

For issues or questions about the autoscaling test script:

1. Check the logs in `../logs/autoscaling-test.log`
2. Review AWS CloudWatch metrics
3. Verify ECS service and ALB health
4. Check application logs for errors

---

**Note**: This script is designed specifically for testing the autoscaling behavior of your ECS Fargate Express application. Monitor AWS costs during testing and adjust load levels as needed for your environment.
