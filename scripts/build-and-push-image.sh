#!/bin/bash

# Build and Push Docker Image to ECR Script
# This script builds the Express application Docker image and pushes it to AWS ECR
# 
# Usage: ./scripts/build-and-push-image.sh [tag] [region]
# Example: ./scripts/build-and-push-image.sh latest ap-southeast-1

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
IMAGE_TAG=${1:-latest}
AWS_REGION=${2:-ap-southeast-1}
REPOSITORY_NAME="express-fargate-app"
DOCKERFILE_PATH="./express-app"

print_status "Starting Docker image build and push process..."
print_status "Image tag: $IMAGE_TAG"
print_status "AWS region: $AWS_REGION"
print_status "Repository name: $REPOSITORY_NAME"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Get AWS account ID
print_status "Getting AWS account ID..."
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
if [ $? -ne 0 ]; then
    print_error "Failed to get AWS account ID. Please check your AWS credentials."
    exit 1
fi
print_success "AWS Account ID: $AWS_ACCOUNT_ID"

# Construct ECR repository URI
ECR_URI="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$REPOSITORY_NAME"
print_status "ECR Repository URI: $ECR_URI"

# Check if ECR repository exists
print_status "Checking if ECR repository exists..."
if ! aws ecr describe-repositories --repository-names $REPOSITORY_NAME --region $AWS_REGION &> /dev/null; then
    print_warning "ECR repository $REPOSITORY_NAME does not exist."
    print_status "Creating ECR repository..."
    
    # Create ECR repository
    aws ecr create-repository \
        --repository-name $REPOSITORY_NAME \
        --region $AWS_REGION \
        --image-scanning-configuration scanOnPush=true || {
        print_error "Failed to create ECR repository"
        exit 1
    }
    
    print_success "ECR repository created successfully"
    
    # Set lifecycle policy separately
    print_status "Setting ECR lifecycle policy..."
    aws ecr put-lifecycle-policy \
        --repository-name $REPOSITORY_NAME \
        --region $AWS_REGION \
        --lifecycle-policy-text '{
            "rules": [
                {
                    "rulePriority": 1,
                    "description": "Keep last 10 production images",
                    "selection": {
                        "tagStatus": "tagged",
                        "tagPrefixList": ["prod"],
                        "countType": "imageCountMoreThan",
                        "countNumber": 10
                    },
                    "action": {
                        "type": "expire"
                    }
                },
                {
                    "rulePriority": 2,
                    "description": "Delete untagged images after 1 day",
                    "selection": {
                        "tagStatus": "untagged",
                        "countType": "sinceImagePushed",
                        "countUnit": "days",
                        "countNumber": 1
                    },
                    "action": {
                        "type": "expire"
                    }
                }
            ]
        }' || {
        print_warning "Failed to set lifecycle policy (repository created successfully)"
    }
    
    print_success "ECR lifecycle policy configured"
else
    print_success "ECR repository already exists"
fi

# Authenticate Docker to ECR
print_status "Authenticating Docker to ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_URI
if [ $? -ne 0 ]; then
    print_error "Failed to authenticate Docker to ECR"
    exit 1
fi
print_success "Docker authenticated to ECR successfully"

# Build the Express application first
print_status "Building Express application..."
cd express-app
if [ -f "package.json" ]; then
    npm install
    npm run build 2>/dev/null || {
        print_warning "No build script found in express-app package.json, skipping build step"
    }
fi
cd ..

# Build Docker image
print_status "Building Docker image for AWS Fargate (linux/amd64 platform)..."
docker build --platform linux/amd64 -t $REPOSITORY_NAME:$IMAGE_TAG $DOCKERFILE_PATH
if [ $? -ne 0 ]; then
    print_error "Failed to build Docker image"
    exit 1
fi
print_success "Docker image built successfully"

# Tag image for ECR
print_status "Tagging image for ECR..."
docker tag $REPOSITORY_NAME:$IMAGE_TAG $ECR_URI:$IMAGE_TAG
if [ $? -ne 0 ]; then
    print_error "Failed to tag Docker image"
    exit 1
fi
print_success "Image tagged successfully"

# Push image to ECR
print_status "Pushing image to ECR..."
docker push $ECR_URI:$IMAGE_TAG
if [ $? -ne 0 ]; then
    print_error "Failed to push Docker image to ECR"
    exit 1
fi
print_success "Image pushed to ECR successfully"

# Also tag and push as 'latest' if not already latest
if [ "$IMAGE_TAG" != "latest" ]; then
    print_status "Tagging and pushing as 'latest'..."
    docker tag $REPOSITORY_NAME:$IMAGE_TAG $ECR_URI:latest
    docker push $ECR_URI:latest
    print_success "Latest tag updated"
fi

# Display image information
print_status "Getting image information..."
IMAGE_DIGEST=$(aws ecr describe-images --repository-name $REPOSITORY_NAME --image-ids imageTag=$IMAGE_TAG --region $AWS_REGION --query 'imageDetails[0].imageDigest' --output text 2>/dev/null || echo "N/A")
IMAGE_SIZE=$(aws ecr describe-images --repository-name $REPOSITORY_NAME --image-ids imageTag=$IMAGE_TAG --region $AWS_REGION --query 'imageDetails[0].imageSizeInBytes' --output text 2>/dev/null || echo "N/A")

echo ""
print_success "=== BUILD AND PUSH COMPLETED SUCCESSFULLY ==="
echo -e "${GREEN}Repository URI:${NC} $ECR_URI"
echo -e "${GREEN}Image Tag:${NC} $IMAGE_TAG"
echo -e "${GREEN}Image Digest:${NC} $IMAGE_DIGEST"
echo -e "${GREEN}Image Size:${NC} $IMAGE_SIZE bytes"
echo ""
print_status "Your ECS service will now be able to pull this image!"
print_status "You can now run: npm run deploy:ecs"
echo "" 