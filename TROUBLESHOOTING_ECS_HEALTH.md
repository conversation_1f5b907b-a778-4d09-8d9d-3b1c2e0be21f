# ECS Fargate Container Health Troubleshooting Guide

## 🔍 **Common Causes of Unhealthy Containers**

### 1. **Missing Dependencies in Container**

The most common issue is missing `curl` in the Alpine container for health checks.

**✅ Fixed**: I've updated the Dockerfile to include `curl`:

```dockerfile
RUN apk add --no-cache dumb-init curl
```

### 2. **Health Check Configuration Issues**

**✅ Fixed**: Updated health check settings:

- Increased timeout from 5s to 10s
- Increased retries from 3 to 5
- Extended startup period from 60s to 120s
- Changed target group health check from TCP to HTTP with `/health` path

### 3. **Application Startup Time**

Your Express app may take longer to start than the health check allows.

**✅ Fixed**: Extended `startPeriod` to 120 seconds to allow proper app initialization.

## 🛠 **Diagnostic Commands**

### Check ECS Service Status

```bash
# View service status
aws ecs describe-services \
  --cluster express-fargate-cluster \
  --services express-fargate-service \
  --region ap-southeast-1

# Check task status and health
aws ecs describe-tasks \
  --cluster express-fargate-cluster \
  --tasks $(aws ecs list-tasks --cluster express-fargate-cluster --service express-fargate-service --query 'taskArns[0]' --output text) \
  --region ap-southeast-1
```

### View Container Logs

```bash
# View real-time logs
aws logs tail /ecs/express-fargate-app --follow --region ap-southeast-1

# View specific time range
aws logs tail /ecs/express-fargate-app \
  --since 1h \
  --region ap-southeast-1
```

### Check Load Balancer Target Health

```bash
# Get target group ARN
aws elbv2 describe-target-groups \
  --names express-fargate-service \
  --region ap-southeast-1

# Check target health (replace TARGET_GROUP_ARN)
aws elbv2 describe-target-health \
  --target-group-arn <TARGET_GROUP_ARN> \
  --region ap-southeast-1
```

## 🔧 **Manual Health Check Testing**

### Test Health Endpoint Directly

Once you get the task's public IP:

```bash
# Get task public IP
aws ecs describe-tasks \
  --cluster express-fargate-cluster \
  --tasks $(aws ecs list-tasks --cluster express-fargate-cluster --service express-fargate-service --query 'taskArns[0]' --output text) \
  --query 'tasks[0].attachments[0].details[?name==`networkInterfaceId`].value' \
  --output text \
  --region ap-southeast-1

# Test health endpoint (replace with actual IP)
curl -v http://<TASK_PUBLIC_IP>:3000/health
```

### Test via Load Balancer

```bash
# Get load balancer DNS name
aws elbv2 describe-load-balancers \
  --names express-fargate-service \
  --query 'LoadBalancers[0].DNSName' \
  --output text \
  --region ap-southeast-1

# Test via load balancer
curl -v http://<LOAD_BALANCER_DNS>/health
```

## 🚀 **Deployment Steps to Fix Issues**

### 1. Rebuild and Deploy

```bash
# Clean rebuild with fixes
npm run docker:build-and-push
npm run deploy:ecs
```

### 2. Monitor Deployment

```bash
# Watch service events
aws ecs describe-services \
  --cluster express-fargate-cluster \
  --services express-fargate-service \
  --region ap-southeast-1 \
  --query 'services[0].events[0:10]'

# Monitor logs during deployment
aws logs tail /ecs/express-fargate-app --follow --region ap-southeast-1
```

## 📊 **Health Check Timeline**

With the updated configuration:

1. **0-120s**: Startup period (health checks ignored)
2. **120s+**: Health checks begin every 30s
3. **2 consecutive passes**: Task marked healthy
4. **5 consecutive failures**: Task marked unhealthy and replaced

## ⚠️ **Common Pitfalls to Avoid**

1. **Port Mismatch**: Ensure container port (3000) matches health check port
2. **Missing Routes**: Verify `/health` endpoint exists and returns 200
3. **Security Groups**: Ensure inbound traffic allowed on port 3000
4. **Resource Constraints**: Monitor CPU/memory usage during startup
5. **Network Issues**: Check VPC configuration and subnet routing

## 🎯 **Quick Fixes Summary**

**✅ Added curl to container**: Required for health checks
**✅ Extended startup period**: 120s for app initialization  
**✅ Increased health check tolerance**: More retries and longer timeout
**✅ Used HTTP health checks**: Better than TCP for detecting app readiness
**✅ Proper health endpoint**: Express app has `/health` route

## 📞 **Next Steps**

1. **Deploy the fixes**: Run `npm run deploy:full`
2. **Monitor logs**: Watch for startup errors
3. **Check target health**: Verify load balancer sees healthy targets
4. **Test endpoints**: Confirm API Gateway responds correctly

If issues persist, check the logs for specific error messages and verify the application starts correctly locally first.
