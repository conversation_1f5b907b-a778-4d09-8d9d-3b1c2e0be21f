{"testMessages": [{"description": "Simple text message", "messageBody": "Hello from SQS! This is a simple test message."}, {"description": "Order processing message", "messageBody": {"action": "process_order", "orderId": "ORD-12345", "customerId": "CUST-67890", "items": [{"productId": "PROD-001", "quantity": 2, "price": 29.99}, {"productId": "PROD-002", "quantity": 1, "price": 49.99}], "totalAmount": 109.97, "timestamp": "2024-01-15T10:30:00Z"}}, {"description": "Notification message", "messageBody": {"action": "send_notification", "type": "email", "recipient": "<EMAIL>", "subject": "Order Confirmation", "message": "Your order has been processed successfully!", "priority": "high"}}, {"description": "Inventory update message", "messageBody": {"action": "update_inventory", "productId": "PROD-001", "quantityChange": -2, "currentStock": 98, "location": "warehouse-east", "reason": "sale"}}, {"description": "Generic data message", "messageBody": {"eventType": "user_activity", "userId": "user123", "activity": "login", "timestamp": "2024-01-15T10:35:00Z", "metadata": {"ipAddress": "*************", "userAgent": "Mozilla/5.0...", "sessionId": "sess_abc123"}}}, {"description": "Message that will cause processing error (for testing error handling)", "messageBody": {"action": "unknown_action", "invalidData": "This message type is not handled", "willCauseError": true}}], "usage": {"note": "Use these test messages to verify your SQS-Lambda integration", "awsCliExample": "aws sqs send-message --queue-url YOUR_QUEUE_URL --message-body 'MESSAGE_BODY_JSON'", "instructions": ["1. Deploy your CDK stack: npm run deploy", "2. Get the queue URL from the CDK output", "3. Send test messages using AWS CLI or AWS Console", "4. <PERSON> CloudWatch Logs to see Lambda function execution"]}}