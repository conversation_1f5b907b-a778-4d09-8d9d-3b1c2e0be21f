# Express Fargate Application

A production-ready Node.js Express application deployed on AWS ECS Fargate using mixed capacity strategies (Fargate + Spot instances) for cost optimization.

## Architecture Overview

### Infrastructure Components - High Performance & Cost Optimized

- **VPC**: Custom VPC with public subnets only across 3 AZs (no NAT Gateway costs)
- **ECS Cluster**: Fargate cluster with mixed capacity providers
- **ECS Tasks**: Run in public subnets with public IPs for direct internet access
- **API Gateway**: Front door with throttling, caching, and API management features
- **Network Load Balancer**: High-performance Layer 4 load balancing with lower latency
- **VPC Link**: Secure connectivity between API Gateway and NLB
- **ECR Repository**: Private container registry for images
- **CloudWatch**: Comprehensive logging and monitoring
- **Auto Scaling**: CPU and memory-based scaling policies

### Capacity Strategy

- **Fargate (20%)**: Reliable, on-demand capacity with base capacity of 1
- **Fargate Spot (80%)**: Cost-optimized capacity for additional scaling

## Application Features

### Express Server

- **Security**: Helmet.js for HTTP headers, CORS configuration
- **Logging**: Winston-based structured logging with request tracking
- **Health Checks**: Comprehensive health endpoints for ECS monitoring
- **Error Handling**: Graceful error handling with proper HTTP responses
- **Graceful Shutdown**: Proper container termination handling

### API Endpoints

| Endpoint        | Method | Description                                          |
| --------------- | ------ | ---------------------------------------------------- |
| `/`             | GET    | Welcome message and API documentation                |
| `/health`       | GET    | Basic health check for load balancer                 |
| `/health/deep`  | GET    | Comprehensive health check with service connectivity |
| `/status`       | GET    | Application status and configuration                 |
| `/api/v1/data`  | GET    | Sample data endpoint                                 |
| `/api/v1/data`  | POST   | Create sample data                                   |
| `/api/v1/error` | GET    | Error simulation for testing                         |

## Prerequisites

- Node.js 18+ installed locally
- AWS CLI configured with appropriate permissions
- Docker installed for container building
- AWS CDK v2 installed (`npm install -g aws-cdk`)

## Local Development

### Setup

```bash
# Navigate to the express app directory
cd express-app

# Install dependencies
npm install

# Build the TypeScript application
npm run build:express

# Start the application locally
npm run start:express
```

### Local Testing

```bash
# Test health endpoint
curl http://localhost:3000/health

# Test API endpoints
curl http://localhost:3000/api/v1/data
```

## Container Deployment

### Build Docker Image

```bash
# Build the Docker image
docker build -t express-fargate-app ./express-app

# Run locally for testing
docker run -p 3000:3000 express-fargate-app

# Test the containerized application
curl http://localhost:3000/health
```

## AWS Deployment

### 1. CDK Deployment

```bash
# Install CDK dependencies (from project root)
npm install

# Bootstrap CDK (first time only)
cdk bootstrap

# Deploy the ECS Fargate stack
cdk deploy EcsFargateAndSpotStack

# Get the outputs
cdk output
```

### 2. Container Image Deployment

```bash
# Get ECR repository URI from CDK output
ECR_URI=$(aws cloudformation describe-stacks --stack-name EcsFargateAndSpotStack --query 'Stacks[0].Outputs[?OutputKey==`ECRRepositoryURI`].OutputValue' --output text)

# Login to ECR
aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin $ECR_URI

# Build and tag image
docker build -t express-fargate-app ./express-app
docker tag express-fargate-app:latest $ECR_URI:latest

# Push image to ECR
docker push $ECR_URI:latest
```

### 3. Update ECS Service

```bash
# Force new deployment with updated image
aws ecs update-service --cluster express-fargate-cluster --service express-fargate-service --force-new-deployment
```

## Monitoring and Logging

### CloudWatch Logs

- **Log Group**: `/ecs/express-fargate-app`
- **Structured Logging**: JSON format with request tracking
- **Log Levels**: Error, Warn, Info, Debug

### CloudWatch Alarms

- **CPU Utilization**: Alerts at 85% threshold
- **Memory Utilization**: Alerts at 90% threshold
- **Auto Scaling**: Triggers based on CPU (70%) and Memory (80%)

### Health Check Monitoring

```bash
# Get API Gateway URL from CDK output
API_URL=$(aws cloudformation describe-stacks --stack-name EcsFargateAndSpotStack --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayURL`].OutputValue' --output text)

# Test health endpoints via API Gateway
curl ${API_URL}health
curl ${API_URL}health/deep

# Get Network Load Balancer DNS for direct testing (if needed)
NLB_DNS=$(aws cloudformation describe-stacks --stack-name EcsFargateAndSpotStack --query 'Stacks[0].Outputs[?OutputKey==`NetworkLoadBalancerDNS`].OutputValue' --output text)

# Direct NLB testing (bypasses API Gateway)
curl http://$NLB_DNS/health
```

## Auto Scaling Configuration

### Scaling Policies

- **Scale Out**: CPU > 70% or Memory > 80%
- **Scale In**: CPU < 70% and Memory < 80%
- **Cooldown**: 2 minutes scale out, 5 minutes scale in
- **Capacity**: Min 2 tasks, Max 20 tasks

### Capacity Strategy

The service uses mixed capacity providers for cost optimization:

```yaml
Capacity Strategy:
  - Fargate: 20% (base: 1 task)
  - Fargate Spot: 80% (additional scaling)
```

## Cost Optimization

### Fargate Spot Benefits

- **Cost Savings**: Up to 70% savings compared to on-demand Fargate
- **Reliability**: Base capacity on regular Fargate ensures availability
- **Automatic Handling**: ECS manages spot interruptions gracefully

### Resource Optimization

- **Right-sizing**: 0.5 vCPU, 1GB memory per task
- **ECR Lifecycle**: Automatic cleanup of old images
- **Log Retention**: 1 week retention for development environment
- **No NAT Gateway**: Tasks in public subnets eliminate ~$90/month NAT Gateway costs
- **Public IP Strategy**: Direct internet access without additional networking costs

## Security Features

### Container Security

- **Non-root User**: Application runs as `expressuser`
- **Minimal Base Image**: Alpine Linux for reduced attack surface
- **Security Headers**: Helmet.js configuration for HTTP security
- **Resource Limits**: CPU and memory limits enforced

### Network Security

- **Public Subnets**: ECS tasks run in public subnets with public IPs (cost optimized)
- **Security Groups**: Controlled access - ALB to ECS tasks only
- **ALB Security**: Internet-facing load balancer with health check integration
- **No Database Access**: Simplified security model without private subnet complexity

## Troubleshooting

### Common Issues

1. **Service Not Starting**

   ```bash
   # Check ECS service status
   aws ecs describe-services --cluster express-fargate-cluster --services express-fargate-service

   # Check task definition
   aws ecs describe-task-definition --task-definition express-fargate-app
   ```

2. **Health Check Failures**

   ```bash
   # Check application logs
   aws logs tail /ecs/express-fargate-app --follow

   # Check load balancer health
   aws elbv2 describe-target-health --target-group-arn <TARGET_GROUP_ARN>
   ```

3. **Scaling Issues**
   ```bash
   # Check auto scaling policies
   aws application-autoscaling describe-scaling-policies --service-namespace ecs
   ```

### Logs and Debugging

```bash
# View real-time logs
aws logs tail /ecs/express-fargate-app --follow

# Filter error logs
aws logs filter-events --log-group-name /ecs/express-fargate-app --filter-pattern "ERROR"

# Check specific time range
aws logs filter-events --log-group-name /ecs/express-fargate-app --start-time 1234567890000
```

## Environment Variables

| Variable          | Default      | Description                       |
| ----------------- | ------------ | --------------------------------- |
| `NODE_ENV`        | `production` | Application environment           |
| `PORT`            | `3000`       | Application port                  |
| `APP_VERSION`     | `1.0.0`      | Application version               |
| `LOG_LEVEL`       | `info`       | Winston log level                 |
| `ALLOWED_ORIGINS` | -            | CORS allowed origins (production) |

## Development Workflow

### Local Development

1. Make changes to source code
2. Build and test locally
3. Build Docker image and test
4. Push to feature branch

### Deployment Pipeline

1. Build Docker image
2. Push to ECR repository
3. Update ECS service
4. Monitor deployment and health checks

## Performance Considerations

### Request Handling

- Connection pooling for external services
- Request timeout configuration (30 seconds)
- Keep-alive timeout (5 seconds)

### Resource Management

- Memory usage monitoring
- CPU utilization tracking
- Graceful degradation on high load

## Support and Maintenance

### Regular Maintenance

- Review CloudWatch alarms and metrics
- Update base Docker images for security patches
- Monitor cost optimization opportunities
- Review and update scaling policies based on usage patterns

### Emergency Procedures

- Scale up manually during high traffic: `aws ecs update-service --desired-count 10`
- Check service health: Monitor `/health/deep` endpoint
- Rollback deployment: Deploy previous ECR image tag

---

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with proper tests
4. Submit a pull request with detailed description
