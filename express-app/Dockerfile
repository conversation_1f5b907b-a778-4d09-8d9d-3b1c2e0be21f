# Multi-stage Docker build for Express Fargate Application
# Optimized for production deployment on AWS ECS Fargate
# Includes security hardening and minimal runtime footprint
# Force x86_64/AMD64 architecture for AWS Fargate compatibility

# Build stage - Compile TypeScript and install dependencies
FROM --platform=linux/amd64 node:18-alpine AS builder

# Set working directory for build
WORKDIR /app

# Create logs directory for Winston logger
RUN mkdir -p logs

# Install build dependencies and security updates
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# Copy package files for dependency installation
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies (including dev dependencies for build)
RUN npm ci && npm cache clean --force

# Copy source code
COPY src/ ./src/

# Build TypeScript application
RUN npm run build:express

# Production stage - Runtime environment
FROM --platform=linux/amd64 node:18-alpine AS production

# Install dumb-init and curl for proper signal handling and health checks
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init curl && \
    rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S expressuser -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Create logs directory with proper permissions
RUN mkdir -p logs && \
    chown -R expressuser:nodejs logs

# Copy production files from builder stage
COPY --from=builder --chown=expressuser:nodejs /app/dist ./dist
COPY --from=builder --chown=expressuser:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=expressuser:nodejs /app/package*.json ./

# Set environment variables for production
ENV NODE_ENV=production
ENV PORT=3000
ENV APP_VERSION=1.0.0

# Expose application port
EXPOSE 3000

# Switch to non-root user
USER expressuser

# Health check configuration for ECS
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# Use dumb-init to handle signals properly in containers
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "dist/app.js"] 