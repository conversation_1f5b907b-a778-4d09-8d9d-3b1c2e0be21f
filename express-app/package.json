{"name": "express-fargate-app", "version": "1.0.0", "description": "Express application for ECS Fargate deployment", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "ts-node src/app.ts", "watch": "tsc -w", "clean": "rm -rf dist", "build:express": "npm run build"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "winston": "^3.10.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/node": "^18.14.6", "typescript": "^4.9.5", "ts-node": "^10.9.1"}, "engines": {"node": ">=18.0.0"}, "keywords": ["express", "typescript", "ecs", "fargate", "docker"], "author": "", "license": "MIT"}