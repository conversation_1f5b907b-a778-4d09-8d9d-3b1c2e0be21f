import express from "express";
import cors from "cors";
import helmet from "helmet";
import {
  logger,
  logAppStart,
  logAppError,
  logAppShutdown,
} from "./services/logger";
import {
  requestLoggerMiddleware,
  errorLoggerMiddleware,
} from "./middleware/requestLogger";
import routes from "./routes/index";

/**
 * Express Application Setup
 * Configures and starts the Express server with comprehensive middleware stack
 * Includes security, logging, CORS, and error handling
 */

/**
 * Express application instance
 * Configured with security middleware, logging, and route handlers
 */
const app = express();

/**
 * Application configuration
 * Environment variables and default settings
 */
const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || "development";
const APP_VERSION = process.env.APP_VERSION || "1.0.0";

/**
 * Security middleware configuration
 * Helmet provides various HTTP headers for security
 * Configured for production deployment with appropriate CSP policies
 */
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    crossOriginEmbedderPolicy: false, // Allow embedding for development
  })
);

/**
 * CORS configuration
 * Allows cross-origin requests with specific configuration
 * Configured based on environment (more restrictive in production)
 */
const corsOptions = {
  origin:
    NODE_ENV === "production"
      ? process.env.ALLOWED_ORIGINS?.split(",") || ["https://yourdomain.com"]
      : true, // Allow all origins in development
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
  credentials: true,
  maxAge: 86400, // 24 hours
};

app.use(cors(corsOptions));

/**
 * Body parsing middleware
 * Handles JSON and URL-encoded request bodies
 * Configured with size limits for security
 */
app.use(
  express.json({
    limit: "10mb",
    strict: true,
  })
);

app.use(
  express.urlencoded({
    extended: true,
    limit: "10mb",
  })
);

/**
 * Request logging middleware
 * Logs all incoming HTTP requests with timing and metadata
 * Uses Winston logger for structured logging
 */
app.use(requestLoggerMiddleware);

/**
 * Trust proxy configuration
 * Required for proper IP address handling behind load balancers
 * ECS Fargate typically runs behind Application Load Balancer
 */
app.set("trust proxy", 1);

/**
 * Application routes
 * Mount all application routes with proper error handling
 */
app.use("/", routes);

/**
 * Global error handling middleware
 * Catches all unhandled errors and provides consistent error responses
 * Must be placed after all routes and middleware
 */
app.use(errorLoggerMiddleware);

/**
 * Final error handler
 * Provides user-friendly error responses without exposing sensitive information
 */
app.use(
  (
    error: Error,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    // Log the error details
    logger.error("Unhandled application error", {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name,
      },
      request: {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get("User-Agent"),
      },
    });

    // Send appropriate error response based on environment
    if (NODE_ENV === "production") {
      res.status(500).json({
        error: "Internal Server Error",
        message: "Something went wrong. Please try again later.",
        timestamp: new Date().toISOString(),
        requestId: (req as any).id || "unknown",
      });
    } else {
      res.status(500).json({
        error: "Internal Server Error",
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        requestId: (req as any).id || "unknown",
      });
    }
  }
);

/**
 * 404 handler for undefined routes
 * Handles requests to non-existent endpoints
 */
app.use("*", (req: express.Request, res: express.Response) => {
  logger.warn("Route not found", {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get("User-Agent"),
  });

  res.status(404).json({
    error: "Not Found",
    message: `Route ${req.method} ${req.originalUrl} not found`,
    timestamp: new Date().toISOString(),
    availableEndpoints: {
      health: "/health",
      status: "/status",
      api: "/api/v1",
    },
  });
});

/**
 * Graceful shutdown handler
 * Handles application shutdown signals for clean container termination
 * Important for ECS Fargate deployment lifecycle
 */
const gracefulShutdown = (signal: string) => {
  logger.info(`Received ${signal}, starting graceful shutdown`);

  server.close((err) => {
    if (err) {
      logger.error("Error during server shutdown", { error: err.message });
      process.exit(1);
    }

    logAppShutdown();
    logger.info("Server closed successfully");
    process.exit(0);
  });

  // Force shutdown after 30 seconds
  setTimeout(() => {
    logger.error("Force shutdown after timeout");
    process.exit(1);
  }, 30000);
};

/**
 * Global error handlers for uncaught exceptions and unhandled rejections
 * Prevents application crashes and ensures proper logging
 */
process.on("uncaughtException", (error: Error) => {
  logger.error("Uncaught Exception", {
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name,
    },
  });

  // Exit gracefully
  process.exit(1);
});

process.on("unhandledRejection", (reason: any, promise: Promise<any>) => {
  logger.error("Unhandled Rejection", {
    reason: reason?.toString(),
    promise: promise?.toString(),
  });

  // Exit gracefully
  process.exit(1);
});

/**
 * Shutdown signal handlers
 * Handle container termination signals from ECS
 */
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));

/**
 * Start the Express server
 * Bind to all network interfaces for container deployment
 */
const server = app.listen(Number(PORT), "0.0.0.0", () => {
  logAppStart(Number(PORT));

  logger.info("Application configuration", {
    version: APP_VERSION,
    environment: NODE_ENV,
    port: PORT,
    nodeVersion: process.version,
    platform: process.platform,
    architecture: process.arch,
    pid: process.pid,
  });
});

/**
 * Server timeout configuration
 * Prevent hanging connections in containerized environment
 */
server.timeout = 30000; // 30 seconds
server.keepAliveTimeout = 5000; // 5 seconds

export default app;
