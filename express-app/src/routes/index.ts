import { Router, Request, Response } from "express";
import { logger } from "../services/logger";

/**
 * Main application routes
 * Defines all HTTP endpoints for the Express application
 * Includes health checks, status endpoints, and sample API routes
 */

const router = Router();

/**
 * Health check endpoint
 * Used by ECS and load balancers to verify application health
 * Returns basic application status and system information
 *
 * @route GET /health
 * @returns {object} Health status and system metrics
 */
router.get("/health", (req: Request, res: Response) => {
  logger.info("Health check requested", {
    ip: req.ip,
    userAgent: req.get("User-Agent"),
  });

  const healthData = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV || "development",
    version: process.env.APP_VERSION || "1.0.0",
    hostname: process.env.HOSTNAME || "local",
  };

  res.status(200).json(healthData);
});

/**
 * Deep health check endpoint
 * More comprehensive health check including external service connectivity
 * Used for detailed application monitoring and debugging
 *
 * @route GET /health/deep
 * @returns {object} Detailed health status with service checks
 */
router.get("/health/deep", async (req: Request, res: Response) => {
  logger.info("Deep health check requested", {
    ip: req.ip,
    userAgent: req.get("User-Agent"),
  });

  const startTime = Date.now();

  try {
    // Simulate external service checks (database, cache, etc.)
    const serviceChecks = await Promise.all([
      checkDatabaseConnection(),
      checkCacheConnection(),
      checkExternalApiConnection(),
    ]);

    const healthData = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV || "development",
      version: process.env.APP_VERSION || "1.0.0",
      hostname: process.env.HOSTNAME || "local",
      services: {
        database: serviceChecks[0],
        cache: serviceChecks[1],
        externalApi: serviceChecks[2],
      },
      responseTime: `${Date.now() - startTime}ms`,
    };

    // Determine overall status based on service checks
    const hasFailures = serviceChecks.some((check) => !check.healthy);
    if (hasFailures) {
      healthData.status = "degraded";
      res.status(503).json(healthData);
    } else {
      res.status(200).json(healthData);
    }
  } catch (error) {
    logger.error("Deep health check failed", {
      error: (error as Error).message,
    });

    res.status(500).json({
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      error: "Health check failed",
      responseTime: `${Date.now() - startTime}ms`,
    });
  }
});

/**
 * Application status endpoint
 * Provides current application metrics and configuration
 *
 * @route GET /status
 * @returns {object} Application status and configuration
 */
router.get("/status", (req: Request, res: Response) => {
  logger.info("Status endpoint requested", {
    ip: req.ip,
    userAgent: req.get("User-Agent"),
  });

  const statusData = {
    application: "Express Fargate App",
    version: process.env.APP_VERSION || "1.0.0",
    environment: process.env.NODE_ENV || "development",
    nodeVersion: process.version,
    platform: process.platform,
    architecture: process.arch,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    pid: process.pid,
    hostname: process.env.HOSTNAME || "local",
    port: process.env.PORT || 3000,
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(statusData);
});

/**
 * Welcome/root endpoint
 * Provides basic application information and available endpoints
 *
 * @route GET /
 * @returns {object} Welcome message and API documentation
 */
router.get("/", (req: Request, res: Response) => {
  logger.info("Root endpoint requested", {
    ip: req.ip,
    userAgent: req.get("User-Agent"),
  });

  const welcomeData = {
    message: "Welcome to Express Fargate Application",
    description:
      "A Node.js Express app running on AWS ECS Fargate with mixed capacity strategies",
    version: process.env.APP_VERSION || "1.0.0",
    environment: process.env.NODE_ENV || "development",
    endpoints: {
      health: "/health",
      deepHealth: "/health/deep",
      status: "/status",
      api: "/api/v1",
    },
    documentation: "https://github.com/your-repo/express-fargate-app",
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(welcomeData);
});

/**
 * Sample API endpoints
 * Demonstrates basic CRUD operations and data handling
 */

/**
 * Get sample data
 * Returns mock data for testing purposes
 *
 * @route GET /api/v1/data
 * @returns {object} Sample data array
 */
router.get("/api/v1/data", (req: Request, res: Response) => {
  logger.info("Sample data requested", {
    ip: req.ip,
    query: req.query,
  });

  const sampleData = {
    data: [
      {
        id: 1,
        name: "Sample Item 1",
        category: "test",
        created: new Date().toISOString(),
      },
      {
        id: 2,
        name: "Sample Item 2",
        category: "demo",
        created: new Date().toISOString(),
      },
      {
        id: 3,
        name: "Sample Item 3",
        category: "example",
        created: new Date().toISOString(),
      },
    ],
    total: 3,
    page: 1,
    timestamp: new Date().toISOString(),
  };

  res.status(200).json(sampleData);
});

/**
 * Create sample data
 * Accepts POST requests to simulate data creation
 *
 * @route POST /api/v1/data
 * @param {object} req.body - Data to create
 * @returns {object} Created data with ID
 */
router.post("/api/v1/data", (req: Request, res: Response) => {
  logger.info("Data creation requested", {
    ip: req.ip,
    body: req.body,
  });

  const newItem = {
    id: Math.floor(Math.random() * 10000),
    ...req.body,
    created: new Date().toISOString(),
    updated: new Date().toISOString(),
  };

  logger.info("Data created successfully", { itemId: newItem.id });

  res.status(201).json({
    message: "Data created successfully",
    data: newItem,
    timestamp: new Date().toISOString(),
  });
});

/**
 * Simulate error endpoint
 * Used for testing error handling and logging
 *
 * @route GET /api/v1/error
 * @returns {error} Simulated error response
 */
router.get("/api/v1/error", (req: Request, res: Response) => {
  logger.warn("Error simulation requested", {
    ip: req.ip,
  });

  const error = new Error("This is a simulated error for testing purposes");
  logger.error("Simulated error thrown", { error: error.message });

  res.status(500).json({
    error: "Internal Server Error",
    message: "This is a simulated error",
    timestamp: new Date().toISOString(),
  });
});

/**
 * Helper functions for health checks
 * Simulate external service connectivity checks
 */

/**
 * Simulate database connection check
 * @returns {Promise<object>} Database health status
 */
async function checkDatabaseConnection(): Promise<{
  name: string;
  healthy: boolean;
  responseTime: number;
}> {
  const startTime = Date.now();

  // Simulate database connection check
  await new Promise((resolve) => setTimeout(resolve, Math.random() * 100));

  return {
    name: "database",
    healthy: Math.random() > 0.1, // 90% success rate
    responseTime: Date.now() - startTime,
  };
}

/**
 * Simulate cache connection check
 * @returns {Promise<object>} Cache health status
 */
async function checkCacheConnection(): Promise<{
  name: string;
  healthy: boolean;
  responseTime: number;
}> {
  const startTime = Date.now();

  // Simulate cache connection check
  await new Promise((resolve) => setTimeout(resolve, Math.random() * 50));

  return {
    name: "cache",
    healthy: Math.random() > 0.05, // 95% success rate
    responseTime: Date.now() - startTime,
  };
}

/**
 * Simulate external API connection check
 * @returns {Promise<object>} External API health status
 */
async function checkExternalApiConnection(): Promise<{
  name: string;
  healthy: boolean;
  responseTime: number;
}> {
  const startTime = Date.now();

  // Simulate external API check
  await new Promise((resolve) => setTimeout(resolve, Math.random() * 200));

  return {
    name: "externalApi",
    healthy: Math.random() > 0.15, // 85% success rate
    responseTime: Date.now() - startTime,
  };
}

export default router;
