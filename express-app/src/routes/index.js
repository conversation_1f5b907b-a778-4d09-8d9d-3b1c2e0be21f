"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const logger_1 = require("../services/logger");
/**
 * Main application routes
 * Defines all HTTP endpoints for the Express application
 * Includes health checks, status endpoints, and sample API routes
 */
const router = (0, express_1.Router)();
/**
 * Health check endpoint
 * Used by ECS and load balancers to verify application health
 * Returns basic application status and system information
 *
 * @route GET /health
 * @returns {object} Health status and system metrics
 */
router.get("/health", (req, res) => {
    logger_1.logger.info("Health check requested", {
        ip: req.ip,
        userAgent: req.get("User-Agent"),
    });
    const healthData = {
        status: "healthy",
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment: process.env.NODE_ENV || "development",
        version: process.env.APP_VERSION || "1.0.0",
        hostname: process.env.HOSTNAME || "local",
    };
    res.status(200).json(healthData);
});
/**
 * Deep health check endpoint
 * More comprehensive health check including external service connectivity
 * Used for detailed application monitoring and debugging
 *
 * @route GET /health/deep
 * @returns {object} Detailed health status with service checks
 */
router.get("/health/deep", async (req, res) => {
    logger_1.logger.info("Deep health check requested", {
        ip: req.ip,
        userAgent: req.get("User-Agent"),
    });
    const startTime = Date.now();
    try {
        // Simulate external service checks (database, cache, etc.)
        const serviceChecks = await Promise.all([
            checkDatabaseConnection(),
            checkCacheConnection(),
            checkExternalApiConnection(),
        ]);
        const healthData = {
            status: "healthy",
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            environment: process.env.NODE_ENV || "development",
            version: process.env.APP_VERSION || "1.0.0",
            hostname: process.env.HOSTNAME || "local",
            services: {
                database: serviceChecks[0],
                cache: serviceChecks[1],
                externalApi: serviceChecks[2],
            },
            responseTime: `${Date.now() - startTime}ms`,
        };
        // Determine overall status based on service checks
        const hasFailures = serviceChecks.some((check) => !check.healthy);
        if (hasFailures) {
            healthData.status = "degraded";
            res.status(503).json(healthData);
        }
        else {
            res.status(200).json(healthData);
        }
    }
    catch (error) {
        logger_1.logger.error("Deep health check failed", {
            error: error.message,
        });
        res.status(500).json({
            status: "unhealthy",
            timestamp: new Date().toISOString(),
            error: "Health check failed",
            responseTime: `${Date.now() - startTime}ms`,
        });
    }
});
/**
 * Application status endpoint
 * Provides current application metrics and configuration
 *
 * @route GET /status
 * @returns {object} Application status and configuration
 */
router.get("/status", (req, res) => {
    logger_1.logger.info("Status endpoint requested", {
        ip: req.ip,
        userAgent: req.get("User-Agent"),
    });
    const statusData = {
        application: "Express Fargate App",
        version: process.env.APP_VERSION || "1.0.0",
        environment: process.env.NODE_ENV || "development",
        nodeVersion: process.version,
        platform: process.platform,
        architecture: process.arch,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        pid: process.pid,
        hostname: process.env.HOSTNAME || "local",
        port: process.env.PORT || 3000,
        timestamp: new Date().toISOString(),
    };
    res.status(200).json(statusData);
});
/**
 * Welcome/root endpoint
 * Provides basic application information and available endpoints
 *
 * @route GET /
 * @returns {object} Welcome message and API documentation
 */
router.get("/", (req, res) => {
    logger_1.logger.info("Root endpoint requested", {
        ip: req.ip,
        userAgent: req.get("User-Agent"),
    });
    const welcomeData = {
        message: "Welcome to Express Fargate Application",
        description: "A Node.js Express app running on AWS ECS Fargate with mixed capacity strategies",
        version: process.env.APP_VERSION || "1.0.0",
        environment: process.env.NODE_ENV || "development",
        endpoints: {
            health: "/health",
            deepHealth: "/health/deep",
            status: "/status",
            api: "/api/v1",
        },
        documentation: "https://github.com/your-repo/express-fargate-app",
        timestamp: new Date().toISOString(),
    };
    res.status(200).json(welcomeData);
});
/**
 * Sample API endpoints
 * Demonstrates basic CRUD operations and data handling
 */
/**
 * Get sample data
 * Returns mock data for testing purposes
 *
 * @route GET /api/v1/data
 * @returns {object} Sample data array
 */
router.get("/api/v1/data", (req, res) => {
    logger_1.logger.info("Sample data requested", {
        ip: req.ip,
        query: req.query,
    });
    const sampleData = {
        data: [
            {
                id: 1,
                name: "Sample Item 1",
                category: "test",
                created: new Date().toISOString(),
            },
            {
                id: 2,
                name: "Sample Item 2",
                category: "demo",
                created: new Date().toISOString(),
            },
            {
                id: 3,
                name: "Sample Item 3",
                category: "example",
                created: new Date().toISOString(),
            },
        ],
        total: 3,
        page: 1,
        timestamp: new Date().toISOString(),
    };
    res.status(200).json(sampleData);
});
/**
 * Create sample data
 * Accepts POST requests to simulate data creation
 *
 * @route POST /api/v1/data
 * @param {object} req.body - Data to create
 * @returns {object} Created data with ID
 */
router.post("/api/v1/data", (req, res) => {
    logger_1.logger.info("Data creation requested", {
        ip: req.ip,
        body: req.body,
    });
    const newItem = {
        id: Math.floor(Math.random() * 10000),
        ...req.body,
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
    };
    logger_1.logger.info("Data created successfully", { itemId: newItem.id });
    res.status(201).json({
        message: "Data created successfully",
        data: newItem,
        timestamp: new Date().toISOString(),
    });
});
/**
 * Simulate error endpoint
 * Used for testing error handling and logging
 *
 * @route GET /api/v1/error
 * @returns {error} Simulated error response
 */
router.get("/api/v1/error", (req, res) => {
    logger_1.logger.warn("Error simulation requested", {
        ip: req.ip,
    });
    const error = new Error("This is a simulated error for testing purposes");
    logger_1.logger.error("Simulated error thrown", { error: error.message });
    res.status(500).json({
        error: "Internal Server Error",
        message: "This is a simulated error",
        timestamp: new Date().toISOString(),
    });
});
/**
 * Helper functions for health checks
 * Simulate external service connectivity checks
 */
/**
 * Simulate database connection check
 * @returns {Promise<object>} Database health status
 */
async function checkDatabaseConnection() {
    const startTime = Date.now();
    // Simulate database connection check
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 100));
    return {
        name: "database",
        healthy: Math.random() > 0.1,
        responseTime: Date.now() - startTime,
    };
}
/**
 * Simulate cache connection check
 * @returns {Promise<object>} Cache health status
 */
async function checkCacheConnection() {
    const startTime = Date.now();
    // Simulate cache connection check
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 50));
    return {
        name: "cache",
        healthy: Math.random() > 0.05,
        responseTime: Date.now() - startTime,
    };
}
/**
 * Simulate external API connection check
 * @returns {Promise<object>} External API health status
 */
async function checkExternalApiConnection() {
    const startTime = Date.now();
    // Simulate external API check
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 200));
    return {
        name: "externalApi",
        healthy: Math.random() > 0.15,
        responseTime: Date.now() - startTime,
    };
}
exports.default = router;
//# sourceMappingURL=data:application/json;base64,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