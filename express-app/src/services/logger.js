"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logServiceConnection = exports.logAppShutdown = exports.logAppError = exports.logAppStart = exports.logRequest = exports.logger = void 0;
const winston = require("winston");
/**
 * Winston logger configuration for the Express application
 * Provides structured logging with different levels and formats
 * Used across all modules for consistent logging patterns
 */
/**
 * Custom log format configuration
 * Combines timestamp, level, message, and additional metadata
 */
const logFormat = winston.format.combine(winston.format.timestamp({
    format: "YYYY-MM-DD HH:mm:ss.SSS",
}), winston.format.errors({ stack: true }), winston.format.json(), winston.format.prettyPrint());
/**
 * Console transport configuration for development
 * Uses colorized output for better readability
 */
const consoleTransport = new winston.transports.Console({
    format: winston.format.combine(winston.format.colorize(), winston.format.simple(), winston.format.printf(({ timestamp, level, message, ...meta }) => {
        return `${timestamp} [${level}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ""}`;
    })),
});
/**
 * File transport configuration for production logging
 * Stores logs in JSON format for structured log analysis
 */
const fileTransport = new winston.transports.File({
    filename: "logs/app.log",
    format: logFormat,
    maxsize: 5242880,
    maxFiles: 5,
});
/**
 * Error-specific file transport
 * Captures only error-level logs for focused debugging
 */
const errorFileTransport = new winston.transports.File({
    filename: "logs/error.log",
    level: "error",
    format: logFormat,
    maxsize: 5242880,
    maxFiles: 5,
});
/**
 * Main Winston logger instance
 * Configured with multiple transports based on environment
 */
exports.logger = winston.createLogger({
    level: process.env.LOG_LEVEL || "info",
    format: logFormat,
    defaultMeta: {
        service: "express-fargate-app",
        environment: process.env.NODE_ENV || "development",
        instance: process.env.HOSTNAME || "local",
    },
    transports: [
        consoleTransport,
        ...(process.env.NODE_ENV === "production"
            ? [fileTransport, errorFileTransport]
            : []),
    ],
    exceptionHandlers: [
        new winston.transports.File({ filename: "logs/exceptions.log" }),
    ],
    rejectionHandlers: [
        new winston.transports.File({ filename: "logs/rejections.log" }),
    ],
});
/**
 * Request logging helper
 * Creates structured logs for HTTP requests with essential metadata
 * @param req - Express request object
 * @param res - Express response object
 * @param duration - Request processing time in milliseconds
 */
const logRequest = (req, res, duration) => {
    const logData = {
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        userAgent: req.get("User-Agent"),
        ip: req.ip,
        duration: `${duration}ms`,
        requestId: req.id || "unknown",
    };
    // Log different levels based on status code
    if (res.statusCode >= 500) {
        exports.logger.error("HTTP Request - Server Error", logData);
    }
    else if (res.statusCode >= 400) {
        exports.logger.warn("HTTP Request - Client Error", logData);
    }
    else {
        exports.logger.info("HTTP Request - Success", logData);
    }
};
exports.logRequest = logRequest;
/**
 * Application lifecycle logging helpers
 * Provides consistent logging for app startup, shutdown, and error scenarios
 */
const logAppStart = (port) => {
    exports.logger.info("Express application started", {
        port,
        nodeVersion: process.version,
        platform: process.platform,
        memory: process.memoryUsage(),
    });
};
exports.logAppStart = logAppStart;
const logAppError = (error) => {
    exports.logger.error("Application error occurred", {
        message: error.message,
        stack: error.stack,
        name: error.name,
    });
};
exports.logAppError = logAppError;
const logAppShutdown = () => {
    exports.logger.info("Express application shutting down", {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
    });
};
exports.logAppShutdown = logAppShutdown;
/**
 * Database/Service connection logging helpers
 * Tracks external service connectivity and performance
 */
const logServiceConnection = (serviceName, success, duration) => {
    const logData = {
        service: serviceName,
        success,
        ...(duration && { duration: `${duration}ms` }),
    };
    if (success) {
        exports.logger.info(`Service connection established: ${serviceName}`, logData);
    }
    else {
        exports.logger.error(`Service connection failed: ${serviceName}`, logData);
    }
};
exports.logServiceConnection = logServiceConnection;
exports.default = exports.logger;
//# sourceMappingURL=data:application/json;base64,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