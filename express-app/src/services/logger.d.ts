import * as winston from "winston";
import { Request, Response } from "express";
/**
 * Main Winston logger instance
 * Configured with multiple transports based on environment
 */
export declare const logger: winston.Logger;
/**
 * Request logging helper
 * Creates structured logs for HTTP requests with essential metadata
 * @param req - Express request object
 * @param res - Express response object
 * @param duration - Request processing time in milliseconds
 */
export declare const logRequest: (req: Request, res: Response, duration: number) => void;
/**
 * Application lifecycle logging helpers
 * Provides consistent logging for app startup, shutdown, and error scenarios
 */
export declare const logAppStart: (port: number) => void;
export declare const logAppError: (error: Error) => void;
export declare const logAppShutdown: () => void;
/**
 * Database/Service connection logging helpers
 * Tracks external service connectivity and performance
 */
export declare const logServiceConnection: (serviceName: string, success: boolean, duration?: number) => void;
export default logger;
