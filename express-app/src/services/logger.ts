import * as winston from "winston";
import { Request, Response } from "express";

/**
 * Winston logger configuration for the Express application
 * Provides structured logging with different levels and formats
 * Used across all modules for consistent logging patterns
 */

/**
 * Custom log format configuration
 * Combines timestamp, level, message, and additional metadata
 */
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: "YYYY-MM-DD HH:mm:ss.SSS",
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

/**
 * Console transport configuration for development
 * Uses colorized output for better readability
 */
const consoleTransport = new winston.transports.Console({
  format: winston.format.combine(
    winston.format.colorize(),
    winston.format.simple(),
    winston.format.printf(({ timestamp, level, message, ...meta }: any) => {
      return `${timestamp} [${level}]: ${message} ${
        Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ""
      }`;
    })
  ),
});

/**
 * File transport configuration for production logging
 * Stores logs in JSON format for structured log analysis
 */
const fileTransport = new winston.transports.File({
  filename: "logs/app.log",
  format: logFormat,
  maxsize: 5242880, // 5MB
  maxFiles: 5,
});

/**
 * Error-specific file transport
 * Captures only error-level logs for focused debugging
 */
const errorFileTransport = new winston.transports.File({
  filename: "logs/error.log",
  level: "error",
  format: logFormat,
  maxsize: 5242880, // 5MB
  maxFiles: 5,
});

/**
 * Main Winston logger instance
 * Configured with multiple transports based on environment
 */
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || "info",
  format: logFormat,
  defaultMeta: {
    service: "express-fargate-app",
    environment: process.env.NODE_ENV || "development",
    instance: process.env.HOSTNAME || "local",
  },
  transports: [
    consoleTransport,
    ...(process.env.NODE_ENV === "production"
      ? [fileTransport, errorFileTransport]
      : []),
  ],
  exceptionHandlers: [
    new winston.transports.File({ filename: "logs/exceptions.log" }),
  ],
  rejectionHandlers: [
    new winston.transports.File({ filename: "logs/rejections.log" }),
  ],
});

/**
 * Request logging helper
 * Creates structured logs for HTTP requests with essential metadata
 * @param req - Express request object
 * @param res - Express response object
 * @param duration - Request processing time in milliseconds
 */
export const logRequest = (req: Request, res: Response, duration: number) => {
  const logData = {
    method: req.method,
    url: req.originalUrl,
    statusCode: res.statusCode,
    userAgent: req.get("User-Agent"),
    ip: req.ip,
    duration: `${duration}ms`,
    requestId: (req as any).id || "unknown",
  };

  // Log different levels based on status code
  if (res.statusCode >= 500) {
    logger.error("HTTP Request - Server Error", logData);
  } else if (res.statusCode >= 400) {
    logger.warn("HTTP Request - Client Error", logData);
  } else {
    logger.info("HTTP Request - Success", logData);
  }
};

/**
 * Application lifecycle logging helpers
 * Provides consistent logging for app startup, shutdown, and error scenarios
 */
export const logAppStart = (port: number) => {
  logger.info("Express application started", {
    port,
    nodeVersion: process.version,
    platform: process.platform,
    memory: process.memoryUsage(),
  });
};

export const logAppError = (error: Error) => {
  logger.error("Application error occurred", {
    message: error.message,
    stack: error.stack,
    name: error.name,
  });
};

export const logAppShutdown = () => {
  logger.info("Express application shutting down", {
    uptime: process.uptime(),
    memory: process.memoryUsage(),
  });
};

/**
 * Database/Service connection logging helpers
 * Tracks external service connectivity and performance
 */
export const logServiceConnection = (
  serviceName: string,
  success: boolean,
  duration?: number
) => {
  const logData = {
    service: serviceName,
    success,
    ...(duration && { duration: `${duration}ms` }),
  };

  if (success) {
    logger.info(`Service connection established: ${serviceName}`, logData);
  } else {
    logger.error(`Service connection failed: ${serviceName}`, logData);
  }
};

export default logger;
