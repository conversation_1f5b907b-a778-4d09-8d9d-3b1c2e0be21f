"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const cors_1 = require("cors");
const helmet_1 = require("helmet");
const logger_1 = require("./services/logger");
const requestLogger_1 = require("./middleware/requestLogger");
const index_1 = require("./routes/index");
/**
 * Express Application Setup
 * Configures and starts the Express server with comprehensive middleware stack
 * Includes security, logging, CORS, and error handling
 */
/**
 * Express application instance
 * Configured with security middleware, logging, and route handlers
 */
const app = (0, express_1.default)();
/**
 * Application configuration
 * Environment variables and default settings
 */
const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || "development";
const APP_VERSION = process.env.APP_VERSION || "1.0.0";
/**
 * Security middleware configuration
 * Helmet provides various HTTP headers for security
 * Configured for production deployment with appropriate CSP policies
 */
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
    crossOriginEmbedderPolicy: false, // Allow embedding for development
}));
/**
 * CORS configuration
 * Allows cross-origin requests with specific configuration
 * Configured based on environment (more restrictive in production)
 */
const corsOptions = {
    origin: NODE_ENV === "production"
        ? process.env.ALLOWED_ORIGINS?.split(",") || ["https://yourdomain.com"]
        : true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
    credentials: true,
    maxAge: 86400, // 24 hours
};
app.use((0, cors_1.default)(corsOptions));
/**
 * Body parsing middleware
 * Handles JSON and URL-encoded request bodies
 * Configured with size limits for security
 */
app.use(express_1.default.json({
    limit: "10mb",
    strict: true,
}));
app.use(express_1.default.urlencoded({
    extended: true,
    limit: "10mb",
}));
/**
 * Request logging middleware
 * Logs all incoming HTTP requests with timing and metadata
 * Uses Winston logger for structured logging
 */
app.use(requestLogger_1.requestLoggerMiddleware);
/**
 * Trust proxy configuration
 * Required for proper IP address handling behind load balancers
 * ECS Fargate typically runs behind Application Load Balancer
 */
app.set("trust proxy", 1);
/**
 * Application routes
 * Mount all application routes with proper error handling
 */
app.use("/", index_1.default);
/**
 * Global error handling middleware
 * Catches all unhandled errors and provides consistent error responses
 * Must be placed after all routes and middleware
 */
app.use(requestLogger_1.errorLoggerMiddleware);
/**
 * Final error handler
 * Provides user-friendly error responses without exposing sensitive information
 */
app.use((error, req, res, next) => {
    // Log the error details
    logger_1.logger.error("Unhandled application error", {
        error: {
            message: error.message,
            stack: error.stack,
            name: error.name,
        },
        request: {
            method: req.method,
            url: req.originalUrl,
            ip: req.ip,
            userAgent: req.get("User-Agent"),
        },
    });
    // Send appropriate error response based on environment
    if (NODE_ENV === "production") {
        res.status(500).json({
            error: "Internal Server Error",
            message: "Something went wrong. Please try again later.",
            timestamp: new Date().toISOString(),
            requestId: req.id || "unknown",
        });
    }
    else {
        res.status(500).json({
            error: "Internal Server Error",
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            requestId: req.id || "unknown",
        });
    }
});
/**
 * 404 handler for undefined routes
 * Handles requests to non-existent endpoints
 */
app.use("*", (req, res) => {
    logger_1.logger.warn("Route not found", {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get("User-Agent"),
    });
    res.status(404).json({
        error: "Not Found",
        message: `Route ${req.method} ${req.originalUrl} not found`,
        timestamp: new Date().toISOString(),
        availableEndpoints: {
            health: "/health",
            status: "/status",
            api: "/api/v1",
        },
    });
});
/**
 * Graceful shutdown handler
 * Handles application shutdown signals for clean container termination
 * Important for ECS Fargate deployment lifecycle
 */
const gracefulShutdown = (signal) => {
    logger_1.logger.info(`Received ${signal}, starting graceful shutdown`);
    server.close((err) => {
        if (err) {
            logger_1.logger.error("Error during server shutdown", { error: err.message });
            process.exit(1);
        }
        (0, logger_1.logAppShutdown)();
        logger_1.logger.info("Server closed successfully");
        process.exit(0);
    });
    // Force shutdown after 30 seconds
    setTimeout(() => {
        logger_1.logger.error("Force shutdown after timeout");
        process.exit(1);
    }, 30000);
};
/**
 * Global error handlers for uncaught exceptions and unhandled rejections
 * Prevents application crashes and ensures proper logging
 */
process.on("uncaughtException", (error) => {
    logger_1.logger.error("Uncaught Exception", {
        error: {
            message: error.message,
            stack: error.stack,
            name: error.name,
        },
    });
    // Exit gracefully
    process.exit(1);
});
process.on("unhandledRejection", (reason, promise) => {
    logger_1.logger.error("Unhandled Rejection", {
        reason: reason?.toString(),
        promise: promise?.toString(),
    });
    // Exit gracefully
    process.exit(1);
});
/**
 * Shutdown signal handlers
 * Handle container termination signals from ECS
 */
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));
/**
 * Start the Express server
 * Bind to all network interfaces for container deployment
 */
const server = app.listen(Number(PORT), "0.0.0.0", () => {
    (0, logger_1.logAppStart)(Number(PORT));
    logger_1.logger.info("Application configuration", {
        version: APP_VERSION,
        environment: NODE_ENV,
        port: PORT,
        nodeVersion: process.version,
        platform: process.platform,
        architecture: process.arch,
        pid: process.pid,
    });
});
/**
 * Server timeout configuration
 * Prevent hanging connections in containerized environment
 */
server.timeout = 30000; // 30 seconds
server.keepAliveTimeout = 5000; // 5 seconds
exports.default = app;
//# sourceMappingURL=data:application/json;base64,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