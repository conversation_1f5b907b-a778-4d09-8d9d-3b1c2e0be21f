import { Request, Response, NextFunction } from "express";
import { logRequest } from "../services/logger";

/**
 * Express middleware for logging HTTP requests
 * Tracks request timing and logs essential request/response metadata
 * Uses Winston logger for structured logging across the application
 */

/**
 * Extended Request interface to include timing information
 * Adds startTime property to track request processing duration
 */
interface TimedRequest extends Request {
  startTime?: number;
}

/**
 * Request logging middleware function
 * Captures request start time and logs completion details
 *
 * @param req - Express request object (extended with timing)
 * @param res - Express response object
 * @param next - Express next function for middleware chain
 */
export const requestLoggerMiddleware = (
  req: TimedRequest,
  res: Response,
  next: NextFunction
): void => {
  // Record request start time for duration calculation
  req.startTime = Date.now();

  // Override the response.end method to capture completion timing
  const originalEnd = res.end;

  res.end = function (chunk?: any, encoding?: any, callback?: any): Response {
    // Calculate request processing duration
    const duration = req.startTime ? Date.now() - req.startTime : 0;

    // Log the completed request with all metadata
    logRequest(req, res, duration);

    // Call the original end method to complete the response
    return originalEnd.call(this, chunk, encoding, callback);
  };

  // Continue to the next middleware
  next();
};

/**
 * Error logging middleware
 * Captures and logs any errors that occur during request processing
 * Should be placed after all other middleware to catch unhandled errors
 *
 * @param error - Error object that was thrown
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const errorLoggerMiddleware = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Import logger locally to avoid circular dependencies
  const { logger } = require("../services/logger");

  // Log the error with request context
  logger.error("Request processing error", {
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name,
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get("User-Agent"),
    },
  });

  // Pass the error to the next error handler
  next(error);
};

export default requestLoggerMiddleware;
