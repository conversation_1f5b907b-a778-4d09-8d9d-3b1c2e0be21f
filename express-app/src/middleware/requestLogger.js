"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorLoggerMiddleware = exports.requestLoggerMiddleware = void 0;
const logger_1 = require("../services/logger");
/**
 * Request logging middleware function
 * Captures request start time and logs completion details
 *
 * @param req - Express request object (extended with timing)
 * @param res - Express response object
 * @param next - Express next function for middleware chain
 */
const requestLoggerMiddleware = (req, res, next) => {
    // Record request start time for duration calculation
    req.startTime = Date.now();
    // Override the response.end method to capture completion timing
    const originalEnd = res.end;
    res.end = function (chunk, encoding, callback) {
        // Calculate request processing duration
        const duration = req.startTime ? Date.now() - req.startTime : 0;
        // Log the completed request with all metadata
        (0, logger_1.logRequest)(req, res, duration);
        // Call the original end method to complete the response
        return originalEnd.call(this, chunk, encoding, callback);
    };
    // Continue to the next middleware
    next();
};
exports.requestLoggerMiddleware = requestLoggerMiddleware;
/**
 * Error logging middleware
 * Captures and logs any errors that occur during request processing
 * Should be placed after all other middleware to catch unhandled errors
 *
 * @param error - Error object that was thrown
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
const errorLoggerMiddleware = (error, req, res, next) => {
    // Import logger locally to avoid circular dependencies
    const { logger } = require("../services/logger");
    // Log the error with request context
    logger.error("Request processing error", {
        error: {
            message: error.message,
            stack: error.stack,
            name: error.name,
        },
        request: {
            method: req.method,
            url: req.originalUrl,
            ip: req.ip,
            userAgent: req.get("User-Agent"),
        },
    });
    // Pass the error to the next error handler
    next(error);
};
exports.errorLoggerMiddleware = errorLoggerMiddleware;
exports.default = exports.requestLoggerMiddleware;
//# sourceMappingURL=data:application/json;base64,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