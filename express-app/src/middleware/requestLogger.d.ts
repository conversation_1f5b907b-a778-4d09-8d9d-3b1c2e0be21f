import { Request, Response, NextFunction } from "express";
/**
 * Express middleware for logging HTTP requests
 * Tracks request timing and logs essential request/response metadata
 * Uses Winston logger for structured logging across the application
 */
/**
 * Extended Request interface to include timing information
 * Adds startTime property to track request processing duration
 */
interface TimedRequest extends Request {
    startTime?: number;
}
/**
 * Request logging middleware function
 * Captures request start time and logs completion details
 *
 * @param req - Express request object (extended with timing)
 * @param res - Express response object
 * @param next - Express next function for middleware chain
 */
export declare const requestLoggerMiddleware: (req: TimedRequest, res: Response, next: NextFunction) => void;
/**
 * Error logging middleware
 * Captures and logs any errors that occur during request processing
 * Should be placed after all other middleware to catch unhandled errors
 *
 * @param error - Error object that was thrown
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export declare const errorLoggerMiddleware: (error: Error, req: Request, res: Response, next: NextFunction) => void;
export default requestLoggerMiddleware;
